/* Style the dropdown container */
.choices {
    background-color: #2d353c; /* Background color */
    color: white; /* Text color */
    border: 1px solid var(--app-component-border-color); /* Optional border */
    border-radius: 5px;
    height: calc(2.02rem + 2px); /* Adjust this value to match .form-control */
}

/* Style the dropdown input */
.choices__inner {
    background-color: #2d353c; /* Background color */
    color: white; /* Text color */
    border: 1px solid #444; /* Optional border */
    padding: 5.5px 7.5px 3.75px !important;
}

/* Style the list of dropdown items */
.choices__list--dropdown {
    background-color: #2d353c; /* Background color for dropdown items */
    border: 1px solid #444; /* Optional border */
}

/* Style each dropdown item */
.choices__item--choice {
    background-color: #2d353c; /* Background color */
    color: white; /* Text color */
    padding: 10px; /* Optional padding */
}

/* Highlight selected dropdown items */
.choices__item--choice.is-highlighted {
    background-color: #444; /* Highlight background color */
    color: white; /* Highlight text color */
}

/* Style selected items in multi-select */
.choices__item {
    background-color: #2d353c; /* Background color */
    color: white; /* Text color */
    border: 0; /* Optional border */
}

/* Style the placeholder text */
.choices__placeholder {
    color: #ccc; /* Placeholder text color */
}

/* Style the search input inside the dropdown */
.choices__input {
    background-color: #2d353c; /* Input background color */
    color: white; /* Input text color */
    border: none; /* Remove border */
}

.choices__item.choices__placeholder.choices__item--selectable {
    border: 0 !important;
}

.choices[data-type*=select-one]::after {
    border-color: white transparent transparent;
}

.choices[data-type*=select-one] .choices__input {
    background-color: #2d353c;
}

.choices__list--dropdown .choices__item--selectable.is-highlighted, .choices__list[aria-expanded] .choices__item--selectable.is-highlighted {
    background-color: #7e7c7c;
}

.choices[data-type*=select-one] .choices__button {
    opacity: 1 !important;
}

.choices__list--single {
    padding: 0 !important;
}

.choices:focus {
    color: var(--app-component-color);
    background-color: var(--app-component-bg);
    border-color: #67abe9;
    outline: 0;
    box-shadow: 0 0 0 .25rem rgba(52, 143, 226, .25);
}

.choices__list--multiple .choices__item {
    font-size: 9px !important;
    margin-bottom: 6px !important;
}