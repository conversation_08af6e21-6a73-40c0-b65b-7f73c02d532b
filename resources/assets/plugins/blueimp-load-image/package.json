{"name": "blueimp-load-image", "version": "5.16.0", "title": "JavaScript Load Image", "description": "JavaScript Load Image is a library to load images provided as File or Blob objects or via URL. It returns an optionally scaled, cropped or rotated HTML img or canvas element. It also provides methods to parse image metadata to extract IPTC and Exif tags as well as embedded thumbnail images, to overwrite the Exif Orientation value and to restore the complete image header after resizing.", "keywords": ["javascript", "load", "loading", "image", "file", "blob", "url", "scale", "crop", "rotate", "img", "canvas", "meta", "exif", "orientation", "thumbnail", "iptc"], "homepage": "https://github.com/blueimp/JavaScript-Load-Image", "author": {"name": "<PERSON>", "url": "https://blueimp.net"}, "repository": {"type": "git", "url": "git://github.com/blueimp/JavaScript-Load-Image.git"}, "license": "MIT", "devDependencies": {"blueimp-canvas-to-blob": "3", "chai": "4", "eslint": "7", "eslint-config-blueimp": "2", "eslint-config-prettier": "8", "eslint-plugin-jsdoc": "36", "eslint-plugin-prettier": "4", "jquery": "1", "mocha": "9", "prettier": "2", "promise-polyfill": "8", "uglify-js": "3"}, "eslintConfig": {"extends": ["blueimp", "plugin:jsdoc/recommended", "plugin:prettier/recommended"], "env": {"browser": true}}, "eslintIgnore": ["js/*.min.js", "js/vendor", "test/vendor"], "prettier": {"arrowParens": "avoid", "proseWrap": "always", "semi": false, "singleQuote": true, "trailingComma": "none"}, "scripts": {"lint": "eslint .", "preunit": "bin/sync-vendor-libs.sh", "unit": "docker-compose run --rm mocha", "test": "npm run lint && npm run unit", "posttest": "docker-compose down -v", "build": "cd js && uglifyjs load-image.js load-image-scale.js load-image-meta.js load-image-fetch.js load-image-orientation.js load-image-exif.js load-image-exif-map.js load-image-iptc.js load-image-iptc-map.js --ie8 -c -m -o load-image.all.min.js --source-map url=load-image.all.min.js.map", "preversion": "npm test", "version": "npm run build && git add -A js", "postversion": "git push --tags origin master master:gh-pages && npm publish"}, "files": ["js/*.js", "js/*.js.map"], "main": "js/index.js"}