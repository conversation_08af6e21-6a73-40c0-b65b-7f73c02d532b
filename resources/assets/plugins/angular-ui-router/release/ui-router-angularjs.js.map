{"version": 3, "file": "ui-router-angularjs.js", "sources": ["angular-ui-router/src/angular.ts", "angular-ui-router/src/statebuilders/views.ts", "angular-ui-router/src/templateFactory.ts", "angular-ui-router/src/stateProvider.ts", "angular-ui-router/src/statebuilders/onEnterExitRetain.ts", "angular-ui-router/src/locationServices.ts", "angular-ui-router/src/urlRouterProvider.ts", "angular-ui-router/src/services.ts", "angular-ui-router/src/directives/stateDirectives.ts", "angular-ui-router/src/stateFilters.ts", "angular-ui-router/src/directives/viewDirective.ts", "angular-ui-router/src/viewScroll.ts", "angular-ui-router/src/index.ts"], "sourcesContent": ["/** @publicapi @module ng1 */ /** */\nimport * as ng_from_import from 'angular';\n/** @hidden */ declare let angular;\n/** @hidden */ const ng_from_global = angular;\n/** @hidden */ export const ng = ng_from_import && ng_from_import.module ? ng_from_import : ng_from_global;\n", "/** @publicapi @module ng1 */ /** */\nimport {\n  StateObject,\n  pick,\n  forEach,\n  tail,\n  extend,\n  isArray,\n  isInjectable,\n  isDefined,\n  isString,\n  services,\n  trace,\n  ViewConfig,\n  ViewService,\n  ViewConfigFactory,\n  PathNode,\n  ResolveContext,\n  Resolvable,\n  IInjectable,\n} from '@uirouter/core';\nimport { Ng1ViewDeclaration } from '../interface';\nimport { TemplateFactory } from '../templateFactory';\n\n/** @internalapi */\nexport function getNg1ViewConfigFactory(): ViewConfigFactory {\n  let templateFactory: TemplateFactory = null;\n  return (path, view) => {\n    templateFactory = templateFactory || services.$injector.get('$templateFactory');\n    return [new Ng1ViewConfig(path, view, templateFactory)];\n  };\n}\n\n/** @internalapi */\nconst hasAnyKey = (keys, obj) => keys.reduce((acc, key) => acc || isDefined(obj[key]), false);\n\n/**\n * This is a [[StateBuilder.builder]] function for angular1 `views`.\n *\n * When the [[StateBuilder]] builds a [[StateObject]] object from a raw [[StateDeclaration]], this builder\n * handles the `views` property with logic specific to @uirouter/angularjs (ng1).\n *\n * If no `views: {}` property exists on the [[StateDeclaration]], then it creates the `views` object\n * and applies the state-level configuration to a view named `$default`.\n *\n * @internalapi\n */\nexport function ng1ViewsBuilder(state: StateObject) {\n  // Do not process root state\n  if (!state.parent) return {};\n\n  const tplKeys = ['templateProvider', 'templateUrl', 'template', 'notify', 'async'],\n    ctrlKeys = ['controller', 'controllerProvider', 'controllerAs', 'resolveAs'],\n    compKeys = ['component', 'bindings', 'componentProvider'],\n    nonCompKeys = tplKeys.concat(ctrlKeys),\n    allViewKeys = compKeys.concat(nonCompKeys);\n\n  // Do not allow a state to have both state-level props and also a `views: {}` property.\n  // A state without a `views: {}` property can declare properties for the `$default` view as properties of the state.\n  // However, the `$default` approach should not be mixed with a separate `views: ` block.\n  if (isDefined(state.views) && hasAnyKey(allViewKeys, state)) {\n    throw new Error(\n      `State '${state.name}' has a 'views' object. ` +\n        `It cannot also have \"view properties\" at the state level.  ` +\n        `Move the following properties into a view (in the 'views' object): ` +\n        ` ${allViewKeys.filter((key) => isDefined(state[key])).join(', ')}`\n    );\n  }\n\n  const views: { [key: string]: Ng1ViewDeclaration } = {},\n    viewsObject = state.views || { $default: pick(state, allViewKeys) };\n\n  forEach(viewsObject, function (config: Ng1ViewDeclaration, name: string) {\n    // Account for views: { \"\": { template... } }\n    name = name || '$default';\n    // Account for views: { header: \"headerComponent\" }\n    if (isString(config)) config = { component: <string>config };\n\n    // Make a shallow copy of the config object\n    config = extend({}, config);\n\n    // Do not allow a view to mix props for component-style view with props for template/controller-style view\n    if (hasAnyKey(compKeys, config) && hasAnyKey(nonCompKeys, config)) {\n      throw new Error(\n        `Cannot combine: ${compKeys.join('|')} with: ${nonCompKeys.join('|')} in stateview: '${name}@${state.name}'`\n      );\n    }\n\n    config.resolveAs = config.resolveAs || '$resolve';\n    config.$type = 'ng1';\n    config.$context = state;\n    config.$name = name;\n\n    const normalized = ViewService.normalizeUIViewTarget(config.$context, config.$name);\n    config.$uiViewName = normalized.uiViewName;\n    config.$uiViewContextAnchor = normalized.uiViewContextAnchor;\n\n    views[name] = config;\n  });\n  return views;\n}\n\n/** @hidden */\nlet id = 0;\n\n/** @internalapi */\nexport class Ng1ViewConfig implements ViewConfig {\n  $id = id++;\n  loaded = false;\n  controller: Function; // actually IInjectable|string\n  template: string;\n  component: string;\n  locals: any; // TODO: delete me\n\n  constructor(public path: PathNode[], public viewDecl: Ng1ViewDeclaration, public factory: TemplateFactory) {}\n\n  load() {\n    const $q = services.$q;\n    const context = new ResolveContext(this.path);\n    const params = this.path.reduce((acc, node) => extend(acc, node.paramValues), {});\n\n    const promises: any = {\n      template: $q.when(this.factory.fromConfig(this.viewDecl, params, context)),\n      controller: $q.when(this.getController(context)),\n    };\n\n    return $q.all(promises).then((results) => {\n      trace.traceViewServiceEvent('Loaded', this);\n      this.controller = results.controller;\n      extend(this, results.template); // Either { template: \"tpl\" } or { component: \"cmpName\" }\n      return this;\n    });\n  }\n\n  getTemplate = (uiView, context: ResolveContext) =>\n    this.component\n      ? this.factory.makeComponentTemplate(uiView, context, this.component, this.viewDecl.bindings)\n      : this.template;\n\n  /**\n   * Gets the controller for a view configuration.\n   *\n   * @returns {Function|Promise.<Function>} Returns a controller, or a promise that resolves to a controller.\n   */\n  getController(context: ResolveContext): IInjectable | string | Promise<IInjectable | string> {\n    const provider = this.viewDecl.controllerProvider;\n    if (!isInjectable(provider)) return this.viewDecl.controller;\n    const deps = services.$injector.annotate(provider);\n    const providerFn = isArray(provider) ? tail(<any>provider) : provider;\n    const resolvable = new Resolvable('', <any>providerFn, deps);\n    return resolvable.get(context);\n  }\n}\n", "/** @publicapi @module view */ /** */\nimport { ng as angular } from './angular';\nimport { IAugmentedJQuery } from 'angular';\nimport {\n  isArray,\n  isDefined,\n  isFunction,\n  isObject,\n  services,\n  Obj,\n  IInjectable,\n  tail,\n  kebobString,\n  unnestR,\n  ResolveContext,\n  Resolvable,\n  RawParams,\n} from '@uirouter/core';\nimport { Ng1ViewDeclaration, TemplateFactoryProvider } from './interface';\n\n/**\n * Service which manages loading of templates from a ViewConfig.\n */\nexport class TemplateFactory implements TemplateFactoryProvider {\n  /** @hidden */ private _useHttp = angular.version.minor < 3;\n  /** @hidden */ private $templateRequest;\n  /** @hidden */ private $templateCache;\n  /** @hidden */ private $http;\n\n  /** @hidden */ $get = [\n    '$http',\n    '$templateCache',\n    '$injector',\n    ($http, $templateCache, $injector) => {\n      this.$templateRequest = $injector.has && $injector.has('$templateRequest') && $injector.get('$templateRequest');\n      this.$http = $http;\n      this.$templateCache = $templateCache;\n      return this;\n    },\n  ];\n\n  /** @hidden */\n  useHttpService(value: boolean) {\n    this._useHttp = value;\n  }\n\n  /**\n   * Creates a template from a configuration object.\n   *\n   * @param config Configuration object for which to load a template.\n   * The following properties are search in the specified order, and the first one\n   * that is defined is used to create the template:\n   *\n   * @param params  Parameters to pass to the template function.\n   * @param context The resolve context associated with the template's view\n   *\n   * @return {string|object}  The template html as a string, or a promise for\n   * that string,or `null` if no template is configured.\n   */\n  fromConfig(\n    config: Ng1ViewDeclaration,\n    params: any,\n    context: ResolveContext\n  ): Promise<{ template?: string; component?: string }> {\n    const defaultTemplate = '<ui-view></ui-view>';\n\n    const asTemplate = (result) => services.$q.when(result).then((str) => ({ template: str }));\n    const asComponent = (result) => services.$q.when(result).then((str) => ({ component: str }));\n\n    return isDefined(config.template)\n      ? asTemplate(this.fromString(config.template, params))\n      : isDefined(config.templateUrl)\n      ? asTemplate(this.fromUrl(config.templateUrl, params))\n      : isDefined(config.templateProvider)\n      ? asTemplate(this.fromProvider(config.templateProvider, params, context))\n      : isDefined(config.component)\n      ? asComponent(config.component)\n      : isDefined(config.componentProvider)\n      ? asComponent(this.fromComponentProvider(config.componentProvider, params, context))\n      : asTemplate(defaultTemplate);\n  }\n\n  /**\n   * Creates a template from a string or a function returning a string.\n   *\n   * @param template html template as a string or function that returns an html template as a string.\n   * @param params Parameters to pass to the template function.\n   *\n   * @return {string|object} The template html as a string, or a promise for that\n   * string.\n   */\n  fromString(template: string | Function, params?: RawParams) {\n    return isFunction(template) ? (<any>template)(params) : template;\n  }\n\n  /**\n   * Loads a template from the a URL via `$http` and `$templateCache`.\n   *\n   * @param {string|Function} url url of the template to load, or a function\n   * that returns a url.\n   * @param {Object} params Parameters to pass to the url function.\n   * @return {string|Promise.<string>} The template html as a string, or a promise\n   * for that string.\n   */\n  fromUrl(url: string | Function, params: any) {\n    if (isFunction(url)) url = (<any>url)(params);\n    if (url == null) return null;\n\n    if (this._useHttp) {\n      return this.$http\n        .get(url, { cache: this.$templateCache, headers: { Accept: 'text/html' } })\n        .then(function (response) {\n          return response.data;\n        });\n    }\n\n    return this.$templateRequest(url);\n  }\n\n  /**\n   * Creates a template by invoking an injectable provider function.\n   *\n   * @param provider Function to invoke via `locals`\n   * @param {Function} injectFn a function used to invoke the template provider\n   * @return {string|Promise.<string>} The template html as a string, or a promise\n   * for that string.\n   */\n  fromProvider(provider: IInjectable, params: any, context: ResolveContext) {\n    const deps = services.$injector.annotate(provider);\n    const providerFn = isArray(provider) ? tail(<any[]>provider) : provider;\n    const resolvable = new Resolvable('', <Function>providerFn, deps);\n    return resolvable.get(context);\n  }\n\n  /**\n   * Creates a component's template by invoking an injectable provider function.\n   *\n   * @param provider Function to invoke via `locals`\n   * @param {Function} injectFn a function used to invoke the template provider\n   * @return {string} The template html as a string: \"<component-name input1='::$resolve.foo'></component-name>\".\n   */\n  fromComponentProvider(provider: IInjectable, params: any, context: ResolveContext) {\n    const deps = services.$injector.annotate(provider);\n    const providerFn = isArray(provider) ? tail(<any[]>provider) : provider;\n    const resolvable = new Resolvable('', <Function>providerFn, deps);\n    return resolvable.get(context);\n  }\n\n  /**\n   * Creates a template from a component's name\n   *\n   * This implements route-to-component.\n   * It works by retrieving the component (directive) metadata from the injector.\n   * It analyses the component's bindings, then constructs a template that instantiates the component.\n   * The template wires input and output bindings to resolves or from the parent component.\n   *\n   * @param uiView {object} The parent ui-view (for binding outputs to callbacks)\n   * @param context The ResolveContext (for binding outputs to callbacks returned from resolves)\n   * @param component {string} Component's name in camel case.\n   * @param bindings An object defining the component's bindings: {foo: '<'}\n   * @return {string} The template as a string: \"<component-name input1='::$resolve.foo'></component-name>\".\n   */\n  makeComponentTemplate(uiView: IAugmentedJQuery, context: ResolveContext, component: string, bindings?: any) {\n    bindings = bindings || {};\n\n    // Bind once prefix\n    const prefix = angular.version.minor >= 3 ? '::' : '';\n    // Convert to kebob name. Add x- prefix if the string starts with `x-` or `data-`\n    const kebob = (camelCase: string) => {\n      const kebobed = kebobString(camelCase);\n      return /^(x|data)-/.exec(kebobed) ? `x-${kebobed}` : kebobed;\n    };\n\n    const attributeTpl = (input: BindingTuple) => {\n      const { name, type } = input;\n      const attrName = kebob(name);\n      // If the ui-view has an attribute which matches a binding on the routed component\n      // then pass that attribute through to the routed component template.\n      // Prefer ui-view wired mappings to resolve data, unless the resolve was explicitly bound using `bindings:`\n      if (uiView.attr(attrName) && !bindings[name]) return `${attrName}='${uiView.attr(attrName)}'`;\n\n      const resolveName = bindings[name] || name;\n      // Pre-evaluate the expression for \"@\" bindings by enclosing in {{ }}\n      // some-attr=\"{{ ::$resolve.someResolveName }}\"\n      if (type === '@') return `${attrName}='{{${prefix}$resolve.${resolveName}}}'`;\n\n      // Wire \"&\" callbacks to resolves that return a callback function\n      // Get the result of the resolve (should be a function) and annotate it to get its arguments.\n      // some-attr=\"$resolve.someResolveResultName(foo, bar)\"\n      if (type === '&') {\n        const res = context.getResolvable(resolveName);\n        const fn = res && res.data;\n        const args = (fn && services.$injector.annotate(fn)) || [];\n        // account for array style injection, i.e., ['foo', function(foo) {}]\n        const arrayIdxStr = isArray(fn) ? `[${fn.length - 1}]` : '';\n        return `${attrName}='$resolve.${resolveName}${arrayIdxStr}(${args.join(',')})'`;\n      }\n\n      // some-attr=\"::$resolve.someResolveName\"\n      return `${attrName}='${prefix}$resolve.${resolveName}'`;\n    };\n\n    const attrs = getComponentBindings(component).map(attributeTpl).join(' ');\n    const kebobName = kebob(component);\n    return `<${kebobName} ${attrs}></${kebobName}>`;\n  }\n}\n\n// Gets all the directive(s)' inputs ('@', '=', and '<') and outputs ('&')\nfunction getComponentBindings(name: string) {\n  const cmpDefs = <any[]>services.$injector.get(name + 'Directive'); // could be multiple\n  if (!cmpDefs || !cmpDefs.length) throw new Error(`Unable to find component named '${name}'`);\n  return cmpDefs.map(getBindings).reduce(unnestR, []);\n}\n\n// Given a directive definition, find its object input attributes\n// Use different properties, depending on the type of directive (component, bindToController, normal)\nconst getBindings = (def: any) => {\n  if (isObject(def.bindToController)) return scopeBindings(def.bindToController);\n  return scopeBindings(def.scope);\n};\n\ninterface BindingTuple {\n  name: string;\n  type: string;\n}\n\n// for ng 1.2 style, process the scope: { input: \"=foo\" }\n// for ng 1.3 through ng 1.5, process the component's bindToController: { input: \"=foo\" } object\nconst scopeBindings = (bindingsObj: Obj) =>\n  Object.keys(bindingsObj || {})\n    // [ 'input', [ '=foo', '=', 'foo' ] ]\n    .map((key) => [key, /^([=<@&])[?]?(.*)/.exec(bindingsObj[key])])\n    // skip malformed values\n    .filter((tuple) => isDefined(tuple) && isArray(tuple[1]))\n    // { name: ('foo' || 'input'), type: '=' }\n    .map((tuple) => ({ name: tuple[1][2] || tuple[0], type: tuple[1][1] } as BindingTuple));\n", "/** @publicapi @module ng1 */ /** */\nimport {\n  val,\n  isObject,\n  createProxyFunctions,\n  BuilderFunction,\n  StateRegistry,\n  StateService,\n  OnInvalidCallback,\n} from '@uirouter/core';\nimport { Ng1StateDeclaration } from './interface';\n\n/**\n * The Angular 1 `StateProvider`\n *\n * The `$stateProvider` works similar to Angular's v1 router, but it focuses purely\n * on state.\n *\n * A state corresponds to a \"place\" in the application in terms of the overall UI and\n * navigation. A state describes (via the controller / template / view properties) what\n * the UI looks like and does at that place.\n *\n * States often have things in common, and the primary way of factoring out these\n * commonalities in this model is via the state hierarchy, i.e. parent/child states aka\n * nested states.\n *\n * The `$stateProvider` provides interfaces to declare these states for your app.\n */\nexport class StateProvider {\n  constructor(private stateRegistry: StateRegistry, private stateService: StateService) {\n    createProxyFunctions(val(StateProvider.prototype), this, val(this));\n  }\n\n  /**\n   * Decorates states when they are registered\n   *\n   * Allows you to extend (carefully) or override (at your own peril) the\n   * `stateBuilder` object used internally by [[StateRegistry]].\n   * This can be used to add custom functionality to ui-router,\n   * for example inferring templateUrl based on the state name.\n   *\n   * When passing only a name, it returns the current (original or decorated) builder\n   * function that matches `name`.\n   *\n   * The builder functions that can be decorated are listed below. Though not all\n   * necessarily have a good use case for decoration, that is up to you to decide.\n   *\n   * In addition, users can attach custom decorators, which will generate new\n   * properties within the state's internal definition. There is currently no clear\n   * use-case for this beyond accessing internal states (i.e. $state.$current),\n   * however, expect this to become increasingly relevant as we introduce additional\n   * meta-programming features.\n   *\n   * **Warning**: Decorators should not be interdependent because the order of\n   * execution of the builder functions in non-deterministic. Builder functions\n   * should only be dependent on the state definition object and super function.\n   *\n   *\n   * Existing builder functions and current return values:\n   *\n   * - **parent** `{object}` - returns the parent state object.\n   * - **data** `{object}` - returns state data, including any inherited data that is not\n   *   overridden by own values (if any).\n   * - **url** `{object}` - returns a {@link ui.router.util.type:UrlMatcher UrlMatcher}\n   *   or `null`.\n   * - **navigable** `{object}` - returns closest ancestor state that has a URL (aka is\n   *   navigable).\n   * - **params** `{object}` - returns an array of state params that are ensured to\n   *   be a super-set of parent's params.\n   * - **views** `{object}` - returns a views object where each key is an absolute view\n   *   name (i.e. \"viewName@stateName\") and each value is the config object\n   *   (template, controller) for the view. Even when you don't use the views object\n   *   explicitly on a state config, one is still created for you internally.\n   *   So by decorating this builder function you have access to decorating template\n   *   and controller properties.\n   * - **ownParams** `{object}` - returns an array of params that belong to the state,\n   *   not including any params defined by ancestor states.\n   * - **path** `{string}` - returns the full path from the root down to this state.\n   *   Needed for state activation.\n   * - **includes** `{object}` - returns an object that includes every state that\n   *   would pass a `$state.includes()` test.\n   *\n   * #### Example:\n   * Override the internal 'views' builder with a function that takes the state\n   * definition, and a reference to the internal function being overridden:\n   * ```js\n   * $stateProvider.decorator('views', function (state, parent) {\n   *   let result = {},\n   *       views = parent(state);\n   *\n   *   angular.forEach(views, function (config, name) {\n   *     let autoName = (state.name + '.' + name).replace('.', '/');\n   *     config.templateUrl = config.templateUrl || '/partials/' + autoName + '.html';\n   *     result[name] = config;\n   *   });\n   *   return result;\n   * });\n   *\n   * $stateProvider.state('home', {\n   *   views: {\n   *     'contact.list': { controller: 'ListController' },\n   *     'contact.item': { controller: 'ItemController' }\n   *   }\n   * });\n   * ```\n   *\n   *\n   * ```js\n   * // Auto-populates list and item views with /partials/home/<USER>/list.html,\n   * // and /partials/home/<USER>/item.html, respectively.\n   * $state.go('home');\n   * ```\n   *\n   * @param {string} name The name of the builder function to decorate.\n   * @param {object} func A function that is responsible for decorating the original\n   * builder function. The function receives two parameters:\n   *\n   *   - `{object}` - state - The state config object.\n   *   - `{object}` - super - The original builder function.\n   *\n   * @return {object} $stateProvider - $stateProvider instance\n   */\n  decorator(name: string, func: BuilderFunction) {\n    return this.stateRegistry.decorator(name, func) || this;\n  }\n\n  /**\n   * Registers a state\n   *\n   * ### This is a passthrough to [[StateRegistry.register]].\n   *\n   * Registers a state configuration under a given state name.\n   * The stateConfig object has the following acceptable properties.\n   *\n   * <a id='template'></a>\n   *\n   * - **`template`** - {string|function=} - html template as a string or a function that returns\n   *   an html template as a string which should be used by the uiView directives. This property\n   *   takes precedence over templateUrl.\n   *\n   *   If `template` is a function, it will be called with the following parameters:\n   *\n   *   - {array.&lt;object&gt;} - state parameters extracted from the current $location.path() by\n   *     applying the current state\n   *\n   * <a id='templateUrl'></a>\n   *\n   * - **`templateUrl`** - {string|function=} - path or function that returns a path to an html\n   *   template that should be used by uiView.\n   *\n   *   If `templateUrl` is a function, it will be called with the following parameters:\n   *\n   *   - {array.&lt;object&gt;} - state parameters extracted from the current $location.path() by\n   *     applying the current state\n   *\n   * <a id='templateProvider'></a>\n   *\n   * - **`templateProvider`** - {function=} - Provider function that returns HTML content\n   *   string.\n   *\n   * <a id='controller'></a>\n   *\n   * - **`controller`** - {string|function=} -  Controller fn that should be associated with newly\n   *   related scope or the name of a registered controller if passed as a string.\n   *\n   * <a id='controllerProvider'></a>\n   *\n   * - **`controllerProvider`** - {function=} - Injectable provider function that returns\n   *   the actual controller or string.\n   *\n   * <a id='controllerAs'></a>\n   *\n   * - **`controllerAs`** – {string=} – A controller alias name. If present the controller will be\n   *   published to scope under the controllerAs name.\n   *\n   * <a id='resolve'></a>\n   *\n   * - **`resolve`** - {object.&lt;string, function&gt;=} - An optional map of dependencies which\n   *   should be injected into the controller. If any of these dependencies are promises,\n   *   the router will wait for them all to be resolved or one to be rejected before the\n   *   controller is instantiated. If all the promises are resolved successfully, the values\n   *   of the resolved promises are injected and $stateChangeSuccess event is fired. If any\n   *   of the promises are rejected the $stateChangeError event is fired. The map object is:\n   *\n   *   - key - {string}: name of dependency to be injected into controller\n   *   - factory - {string|function}: If string then it is alias for service. Otherwise if function,\n   *     it is injected and return value it treated as dependency. If result is a promise, it is\n   *     resolved before its value is injected into controller.\n   *\n   * <a id='url'></a>\n   *\n   * - **`url`** - {string=} - A url with optional parameters. When a state is navigated or\n   *   transitioned to, the `$stateParams` service will be populated with any\n   *   parameters that were passed.\n   *\n   * <a id='params'></a>\n   *\n   * - **`params`** - {object=} - An array of parameter names or regular expressions. Only\n   *   use this within a state if you are not using url. Otherwise you can specify your\n   *   parameters within the url. When a state is navigated or transitioned to, the\n   *   $stateParams service will be populated with any parameters that were passed.\n   *\n   * <a id='views'></a>\n   *\n   * - **`views`** - {object=} - Use the views property to set up multiple views or to target views\n   *   manually/explicitly.\n   *\n   * <a id='abstract'></a>\n   *\n   * - **`abstract`** - {boolean=} - An abstract state will never be directly activated,\n   *   but can provide inherited properties to its common children states.\n   *\n   * <a id='onEnter'></a>\n   *\n   * - **`onEnter`** - {object=} - Callback function for when a state is entered. Good way\n   *   to trigger an action or dispatch an event, such as opening a dialog.\n   * If minifying your scripts, make sure to use the `['injection1', 'injection2', function(injection1, injection2){}]` syntax.\n   *\n   * <a id='onExit'></a>\n   *\n   * - **`onExit`** - {object=} - Callback function for when a state is exited. Good way to\n   *   trigger an action or dispatch an event, such as opening a dialog.\n   * If minifying your scripts, make sure to use the `['injection1', 'injection2', function(injection1, injection2){}]` syntax.\n   *\n   * <a id='reloadOnSearch'></a>\n   *\n   * - **`reloadOnSearch = true`** - {boolean=} - If `false`, will not retrigger the same state\n   *   just because a search/query parameter has changed (via $location.search() or $location.hash()).\n   *   Useful for when you'd like to modify $location.search() without triggering a reload.\n   *\n   * <a id='data'></a>\n   *\n   * - **`data`** - {object=} - Arbitrary data object, useful for custom configuration.\n   *\n   * #### Example:\n   * Some state name examples\n   * ```js\n   * // stateName can be a single top-level name (must be unique).\n   * $stateProvider.state(\"home\", {});\n   *\n   * // Or it can be a nested state name. This state is a child of the\n   * // above \"home\" state.\n   * $stateProvider.state(\"home.newest\", {});\n   *\n   * // Nest states as deeply as needed.\n   * $stateProvider.state(\"home.newest.abc.xyz.inception\", {});\n   *\n   * // state() returns $stateProvider, so you can chain state declarations.\n   * $stateProvider\n   *   .state(\"home\", {})\n   *   .state(\"about\", {})\n   *   .state(\"contacts\", {});\n   * ```\n   *\n   * @param {string} name A unique state name, e.g. \"home\", \"about\", \"contacts\".\n   * To create a parent/child state use a dot, e.g. \"about.sales\", \"home.newest\".\n   * @param {object} definition State configuration object.\n   */\n  state(name: string, definition: Ng1StateDeclaration): StateProvider;\n  state(definition: Ng1StateDeclaration): StateProvider;\n  state(name: any, definition?: any) {\n    if (isObject(name)) {\n      definition = name;\n    } else {\n      definition.name = name;\n    }\n    this.stateRegistry.register(definition);\n    return this;\n  }\n\n  /**\n   * Registers an invalid state handler\n   *\n   * This is a passthrough to [[StateService.onInvalid]] for ng1.\n   */\n\n  onInvalid(callback: OnInvalidCallback): Function {\n    return this.stateService.onInvalid(callback);\n  }\n}\n", "/** @publicapi @module ng1 */ /** */\nimport {\n  StateObject,\n  TransitionStateHookFn,\n  HookResult,\n  Transition,\n  services,\n  ResolveContext,\n  extend,\n} from '@uirouter/core';\nimport { getLocals } from '../services';\nimport { Ng1StateDeclaration } from '../interface';\n\n/**\n * This is a [[StateBuilder.builder]] function for angular1 `onEnter`, `onExit`,\n * `onRetain` callback hooks on a [[Ng1StateDeclaration]].\n *\n * When the [[StateBuilder]] builds a [[StateObject]] object from a raw [[StateDeclaration]], this builder\n * ensures that those hooks are injectable for @uirouter/angularjs (ng1).\n *\n * @internalapi\n */\nexport const getStateHookBuilder = (hookName: 'onEnter' | 'onExit' | 'onRetain') =>\n  function stateHookBuilder(stateObject: StateObject): TransitionStateHookFn {\n    const hook = stateObject[hookName];\n    const pathname = hookName === 'onExit' ? 'from' : 'to';\n\n    function decoratedNg1Hook(trans: Transition, state: Ng1StateDeclaration): HookResult {\n      const resolveContext = new ResolveContext(trans.treeChanges(pathname));\n      const subContext = resolveContext.subContext(state.$$state());\n      const locals = extend(getLocals(subContext), { $state$: state, $transition$: trans });\n      return services.$injector.invoke(hook, this, locals);\n    }\n\n    return hook ? decoratedNg1Hook : undefined;\n  };\n", "/** @publicapi @module ng1 */ /** */\nimport { LocationConfig, LocationServices, UIRouter, ParamType, isDefined } from '@uirouter/core';\nimport { val, createProxyFunctions, removeFrom, isObject } from '@uirouter/core';\nimport { ILocationService, ILocationProvider, IWindowService } from 'angular';\n\n/**\n * Implements UI-Router LocationServices and LocationConfig using Angular 1's $location service\n * @internalapi\n */\nexport class Ng1LocationServices implements LocationConfig, LocationServices {\n  private $locationProvider: ILocationProvider;\n  private $location: ILocationService;\n  private $sniffer: any;\n  private $browser: any;\n  private $window: IWindowService;\n\n  path;\n  search;\n  hash;\n  hashPrefix;\n  port;\n  protocol;\n  host;\n\n  private _baseHref: string;\n\n  // .onChange() registry\n  private _urlListeners: Function[] = [];\n\n  /**\n   * Applys ng1-specific path parameter encoding\n   *\n   * The Angular 1 `$location` service is a bit weird.\n   * It doesn't allow slashes to be encoded/decoded bi-directionally.\n   *\n   * See the writeup at https://github.com/angular-ui/ui-router/issues/2598\n   *\n   * This code patches the `path` parameter type so it encoded/decodes slashes as ~2F\n   *\n   * @param router\n   */\n  static monkeyPatchPathParameterType(router: UIRouter) {\n    const pathType: ParamType = router.urlMatcherFactory.type('path');\n\n    pathType.encode = (x: any) =>\n      x != null ? x.toString().replace(/(~|\\/)/g, (m) => ({ '~': '~~', '/': '~2F' }[m])) : x;\n\n    pathType.decode = (x: string) =>\n      x != null ? x.toString().replace(/(~~|~2F)/g, (m) => ({ '~~': '~', '~2F': '/' }[m])) : x;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  dispose() {}\n\n  constructor($locationProvider: ILocationProvider) {\n    this.$locationProvider = $locationProvider;\n    const _lp = val($locationProvider);\n    createProxyFunctions(_lp, this, _lp, ['hashPrefix']);\n  }\n\n  onChange(callback: Function) {\n    this._urlListeners.push(callback);\n    return () => removeFrom(this._urlListeners)(callback);\n  }\n\n  html5Mode() {\n    let html5Mode: any = this.$locationProvider.html5Mode();\n    html5Mode = isObject(html5Mode) ? html5Mode.enabled : html5Mode;\n    return html5Mode && this.$sniffer.history;\n  }\n\n  baseHref() {\n    return this._baseHref || (this._baseHref = this.$browser.baseHref() || this.$window.location.pathname);\n  }\n\n  url(newUrl?: string, replace = false, state?) {\n    if (isDefined(newUrl)) this.$location.url(newUrl);\n    if (replace) this.$location.replace();\n    if (state) this.$location.state(state);\n    return this.$location.url();\n  }\n\n  _runtimeServices($rootScope, $location: ILocationService, $sniffer, $browser, $window: IWindowService) {\n    this.$location = $location;\n    this.$sniffer = $sniffer;\n    this.$browser = $browser;\n    this.$window = $window;\n\n    // Bind $locationChangeSuccess to the listeners registered in LocationService.onChange\n    $rootScope.$on('$locationChangeSuccess', (evt) => this._urlListeners.forEach((fn) => fn(evt)));\n    const _loc = val($location);\n\n    // Bind these LocationService functions to $location\n    createProxyFunctions(_loc, this, _loc, ['replace', 'path', 'search', 'hash']);\n    // Bind these LocationConfig functions to $location\n    createProxyFunctions(_loc, this, _loc, ['port', 'protocol', 'host']);\n  }\n}\n", "/** @publicapi @module url */ /** */\nimport {\n  UIRouter,\n  LocationServices,\n  $InjectorLike,\n  BaseUrlRule,\n  UrlRuleHandlerFn,\n  UrlMatcher,\n  IInjectable,\n  UrlRouter,\n} from '@uirouter/core';\nimport { services, isString, isFunction, isArray, identity } from '@uirouter/core';\n\nexport interface RawNg1RuleFunction {\n  ($injector: $InjectorLike, $location: LocationServices): string | void;\n}\n\n/**\n * Manages rules for client-side URL\n *\n * ### Deprecation warning:\n * This class is now considered to be an internal API\n * Use the [[UrlService]] instead.\n * For configuring URL rules, use the [[UrlRulesApi]] which can be found as [[UrlService.rules]].\n *\n * This class manages the router rules for what to do when the URL changes.\n *\n * This provider remains for backwards compatibility.\n *\n * @internalapi\n * @deprecated\n */\nexport class UrlRouterProvider {\n  static injectableHandler(router: UIRouter, handler: IInjectable): UrlRuleHandlerFn {\n    return (match) => services.$injector.invoke(handler, null, { $match: match, $stateParams: router.globals.params });\n  }\n\n  /** @hidden */\n  constructor(/** @hidden */ private router: UIRouter) {}\n\n  /** @hidden */\n  $get(): UrlRouter {\n    const urlService = this.router.urlService;\n    this.router.urlRouter.update(true);\n    if (!urlService.interceptDeferred) urlService.listen();\n    return this.router.urlRouter;\n  }\n\n  /**\n   * Registers a url handler function.\n   *\n   * Registers a low level url handler (a `rule`).\n   * A rule detects specific URL patterns and returns a redirect, or performs some action.\n   *\n   * If a rule returns a string, the URL is replaced with the string, and all rules are fired again.\n   *\n   * #### Example:\n   * ```js\n   * var app = angular.module('app', ['ui.router.router']);\n   *\n   * app.config(function ($urlRouterProvider) {\n   *   // Here's an example of how you might allow case insensitive urls\n   *   $urlRouterProvider.rule(function ($injector, $location) {\n   *     var path = $location.path(),\n   *         normalized = path.toLowerCase();\n   *\n   *     if (path !== normalized) {\n   *       return normalized;\n   *     }\n   *   });\n   * });\n   * ```\n   *\n   * @param ruleFn\n   * Handler function that takes `$injector` and `$location` services as arguments.\n   * You can use them to detect a url and return a different url as a string.\n   *\n   * @return [[UrlRouterProvider]] (`this`)\n   */\n  rule(ruleFn: RawNg1RuleFunction): UrlRouterProvider {\n    if (!isFunction(ruleFn)) throw new Error(\"'rule' must be a function\");\n\n    const match = () => ruleFn(services.$injector, this.router.locationService);\n\n    const rule = new BaseUrlRule(match, identity);\n    this.router.urlService.rules.rule(rule);\n    return this;\n  }\n\n  /**\n   * Defines the path or behavior to use when no url can be matched.\n   *\n   * #### Example:\n   * ```js\n   * var app = angular.module('app', ['ui.router.router']);\n   *\n   * app.config(function ($urlRouterProvider) {\n   *   // if the path doesn't match any of the urls you configured\n   *   // otherwise will take care of routing the user to the\n   *   // specified url\n   *   $urlRouterProvider.otherwise('/index');\n   *\n   *   // Example of using function rule as param\n   *   $urlRouterProvider.otherwise(function ($injector, $location) {\n   *     return '/a/valid/url';\n   *   });\n   * });\n   * ```\n   *\n   * @param rule\n   * The url path you want to redirect to or a function rule that returns the url path or performs a `$state.go()`.\n   * The function version is passed two params: `$injector` and `$location` services, and should return a url string.\n   *\n   * @return {object} `$urlRouterProvider` - `$urlRouterProvider` instance\n   */\n  otherwise(rule: string | RawNg1RuleFunction): UrlRouterProvider {\n    const urlRules = this.router.urlService.rules;\n    if (isString(rule)) {\n      urlRules.otherwise(rule);\n    } else if (isFunction(rule)) {\n      urlRules.otherwise(() => rule(services.$injector, this.router.locationService));\n    } else {\n      throw new Error(\"'rule' must be a string or function\");\n    }\n\n    return this;\n  }\n\n  /**\n   * Registers a handler for a given url matching.\n   *\n   * If the handler is a string, it is\n   * treated as a redirect, and is interpolated according to the syntax of match\n   * (i.e. like `String.replace()` for `RegExp`, or like a `UrlMatcher` pattern otherwise).\n   *\n   * If the handler is a function, it is injectable.\n   * It gets invoked if `$location` matches.\n   * You have the option of inject the match object as `$match`.\n   *\n   * The handler can return\n   *\n   * - **falsy** to indicate that the rule didn't match after all, then `$urlRouter`\n   *   will continue trying to find another one that matches.\n   * - **string** which is treated as a redirect and passed to `$location.url()`\n   * - **void** or any **truthy** value tells `$urlRouter` that the url was handled.\n   *\n   * #### Example:\n   * ```js\n   * var app = angular.module('app', ['ui.router.router']);\n   *\n   * app.config(function ($urlRouterProvider) {\n   *   $urlRouterProvider.when($state.url, function ($match, $stateParams) {\n   *     if ($state.$current.navigable !== state ||\n   *         !equalForKeys($match, $stateParams) {\n   *      $state.transitionTo(state, $match, false);\n   *     }\n   *   });\n   * });\n   * ```\n   *\n   * @param what A pattern string to match, compiled as a [[UrlMatcher]].\n   * @param handler The path (or function that returns a path) that you want to redirect your user to.\n   * @param ruleCallback [optional] A callback that receives the `rule` registered with [[UrlMatcher.rule]]\n   *\n   * Note: the handler may also invoke arbitrary code, such as `$state.go()`\n   */\n  when(what: RegExp | UrlMatcher | string, handler: string | IInjectable): this {\n    if (isArray(handler) || isFunction(handler)) {\n      handler = UrlRouterProvider.injectableHandler(this.router, handler);\n    }\n\n    this.router.urlService.rules.when(what, handler as any);\n    return this;\n  }\n\n  /**\n   * Disables monitoring of the URL.\n   *\n   * Call this method before UI-Router has bootstrapped.\n   * It will stop UI-Router from performing the initial url sync.\n   *\n   * This can be useful to perform some asynchronous initialization before the router starts.\n   * Once the initialization is complete, call [[listen]] to tell UI-Router to start watching and synchronizing the URL.\n   *\n   * #### Example:\n   * ```js\n   * var app = angular.module('app', ['ui.router']);\n   *\n   * app.config(function ($urlRouterProvider) {\n   *   // Prevent $urlRouter from automatically intercepting URL changes;\n   *   $urlRouterProvider.deferIntercept();\n   * })\n   *\n   * app.run(function (MyService, $urlRouter, $http) {\n   *   $http.get(\"/stuff\").then(function(resp) {\n   *     MyService.doStuff(resp.data);\n   *     $urlRouter.listen();\n   *     $urlRouter.sync();\n   *   });\n   * });\n   * ```\n   *\n   * @param defer Indicates whether to defer location change interception.\n   *        Passing no parameter is equivalent to `true`.\n   */\n  deferIntercept(defer?: boolean): void {\n    this.router.urlService.deferIntercept(defer);\n  }\n}\n", "/* eslint-disable @typescript-eslint/no-empty-function */\n/* eslint-disable @typescript-eslint/no-unused-vars */\n/**\n * # Angular 1 types\n *\n * UI-Router core provides various Typescript types which you can use for code completion and validating parameter values, etc.\n * The customizations to the core types for Angular UI-Router are documented here.\n *\n * The optional [[$resolve]] service is also documented here.\n *\n * @preferred @publicapi @module ng1\n */ /** */\nimport { ng as angular } from './angular';\nimport {\n  IRootScopeService,\n  IQService,\n  ILocationService,\n  ILocationProvider,\n  IHttpService,\n  ITemplateCacheService,\n} from 'angular';\nimport {\n  services,\n  applyPairs,\n  isString,\n  trace,\n  extend,\n  UIRouter,\n  StateService,\n  UrlRouter,\n  UrlMatcherFactory,\n  ResolveContext,\n  unnestR,\n  TypedMap,\n} from '@uirouter/core';\nimport { ng1ViewsBuilder, getNg1ViewConfigFactory } from './statebuilders/views';\nimport { TemplateFactory } from './templateFactory';\nimport { StateProvider } from './stateProvider';\nimport { getStateHookBuilder } from './statebuilders/onEnterExitRetain';\nimport { Ng1LocationServices } from './locationServices';\nimport { UrlRouterProvider } from './urlRouterProvider';\nimport IInjectorService = angular.auto.IInjectorService;\n\nangular.module('ui.router.angular1', []);\nconst mod_init = angular.module('ui.router.init', ['ng']);\nconst mod_util = angular.module('ui.router.util', ['ui.router.init']);\nconst mod_rtr = angular.module('ui.router.router', ['ui.router.util']);\nconst mod_state = angular.module('ui.router.state', ['ui.router.router', 'ui.router.util', 'ui.router.angular1']);\nconst mod_main = angular.module('ui.router', ['ui.router.init', 'ui.router.state', 'ui.router.angular1']);\nconst mod_cmpt = angular.module('ui.router.compat', ['ui.router']);\n\ndeclare module '@uirouter/core/lib/router' {\n  interface UIRouter {\n    /** @hidden */\n    stateProvider: StateProvider;\n    /** @hidden */\n    urlRouterProvider: UrlRouterProvider;\n  }\n}\n\nlet router: UIRouter = null;\n\n$uiRouterProvider.$inject = ['$locationProvider'];\n/** This angular 1 provider instantiates a Router and exposes its services via the angular injector */\nfunction $uiRouterProvider($locationProvider: ILocationProvider) {\n  // Create a new instance of the Router when the $uiRouterProvider is initialized\n  router = this.router = new UIRouter();\n  router.stateProvider = new StateProvider(router.stateRegistry, router.stateService);\n\n  // Apply ng1 specific StateBuilder code for `views`, `resolve`, and `onExit/Retain/Enter` properties\n  router.stateRegistry.decorator('views', ng1ViewsBuilder);\n  router.stateRegistry.decorator('onExit', getStateHookBuilder('onExit'));\n  router.stateRegistry.decorator('onRetain', getStateHookBuilder('onRetain'));\n  router.stateRegistry.decorator('onEnter', getStateHookBuilder('onEnter'));\n\n  router.viewService._pluginapi._viewConfigFactory('ng1', getNg1ViewConfigFactory());\n\n  // Disable decoding of params by UrlMatcherFactory because $location already handles this\n  router.urlService.config._decodeParams = false;\n\n  const ng1LocationService = (router.locationService = router.locationConfig = new Ng1LocationServices(\n    $locationProvider\n  ));\n\n  Ng1LocationServices.monkeyPatchPathParameterType(router);\n\n  // backwards compat: also expose router instance as $uiRouterProvider.router\n  router['router'] = router;\n  router['$get'] = $get;\n  $get.$inject = ['$location', '$browser', '$window', '$sniffer', '$rootScope', '$http', '$templateCache'];\n  function $get(\n    $location: ILocationService,\n    $browser: any,\n    $window: any,\n    $sniffer: any,\n    $rootScope: ng.IScope,\n    $http: IHttpService,\n    $templateCache: ITemplateCacheService\n  ) {\n    ng1LocationService._runtimeServices($rootScope, $location, $sniffer, $browser, $window);\n    delete router['router'];\n    delete router['$get'];\n    return router;\n  }\n  return router;\n}\n\nconst getProviderFor = (serviceName) => [\n  '$uiRouterProvider',\n  ($urp) => {\n    const service = $urp.router[serviceName];\n    service['$get'] = () => service;\n    return service;\n  },\n];\n\n// This effectively calls $get() on `$uiRouterProvider` to trigger init (when ng enters runtime)\nrunBlock.$inject = ['$injector', '$q', '$uiRouter'];\nfunction runBlock($injector: IInjectorService, $q: IQService, $uiRouter: UIRouter) {\n  services.$injector = $injector;\n  services.$q = <any>$q;\n\n  // https://github.com/angular-ui/ui-router/issues/3678\n  if (!Object.prototype.hasOwnProperty.call($injector, 'strictDi')) {\n    try {\n      $injector.invoke(function (checkStrictDi) {});\n    } catch (error) {\n      $injector.strictDi = !!/strict mode/.exec(error && error.toString());\n    }\n  }\n\n  // The $injector is now available.\n  // Find any resolvables that had dependency annotation deferred\n  $uiRouter.stateRegistry\n    .get()\n    .map((x) => x.$$state().resolvables)\n    .reduce(unnestR, [])\n    .filter((x) => x.deps === 'deferred')\n    .forEach((resolvable) => (resolvable.deps = $injector.annotate(resolvable.resolveFn, $injector.strictDi)));\n}\n\n// $urlRouter service and $urlRouterProvider\nconst getUrlRouterProvider = (uiRouter: UIRouter) => (uiRouter.urlRouterProvider = new UrlRouterProvider(uiRouter));\n\n// $state service and $stateProvider\n// $urlRouter service and $urlRouterProvider\nconst getStateProvider = () => extend(router.stateProvider, { $get: () => router.stateService });\n\nwatchDigests.$inject = ['$rootScope'];\nexport function watchDigests($rootScope: IRootScopeService) {\n  $rootScope.$watch(function () {\n    trace.approximateDigests++;\n  });\n}\n\nmod_init.provider('$uiRouter', <any>$uiRouterProvider);\nmod_rtr.provider('$urlRouter', ['$uiRouterProvider', getUrlRouterProvider]);\nmod_util.provider('$urlService', getProviderFor('urlService'));\nmod_util.provider('$urlMatcherFactory', ['$uiRouterProvider', () => router.urlMatcherFactory]);\nmod_util.provider('$templateFactory', () => new TemplateFactory());\nmod_state.provider('$stateRegistry', getProviderFor('stateRegistry'));\nmod_state.provider('$uiRouterGlobals', getProviderFor('globals'));\nmod_state.provider('$transitions', getProviderFor('transitionService'));\nmod_state.provider('$state', ['$uiRouterProvider', getStateProvider]);\n\nmod_state.factory('$stateParams', ['$uiRouter', ($uiRouter: UIRouter) => $uiRouter.globals.params]);\nmod_main.factory('$view', () => router.viewService);\nmod_main.service('$trace', () => trace);\n\nmod_main.run(watchDigests);\nmod_util.run(['$urlMatcherFactory', function ($urlMatcherFactory: UrlMatcherFactory) {}]);\nmod_state.run(['$state', function ($state: StateService) {}]);\nmod_rtr.run(['$urlRouter', function ($urlRouter: UrlRouter) {}]);\nmod_init.run(runBlock);\n\n/** @hidden TODO: find a place to move this */\nexport const getLocals = (ctx: ResolveContext): TypedMap<any> => {\n  const tokens = ctx.getTokens().filter(isString);\n\n  const tuples = tokens.map((key) => {\n    const resolvable = ctx.getResolvable(key);\n    const waitPolicy = ctx.getPolicy(resolvable).async;\n    return [key, waitPolicy === 'NOWAIT' ? resolvable.promise : resolvable.data];\n  });\n\n  return tuples.reduce(applyPairs, {});\n};\n", "/* eslint-disable @typescript-eslint/no-empty-interface */\n/* eslint-disable prefer-const */\n/**\n * # Angular 1 Directives\n *\n * These are the directives included in UI-Router for Angular 1.\n * These directives are used in templates to create viewports and link/navigate to states.\n *\n * @preferred @publicapi @module directives\n */ /** */\nimport { ng as angular } from '../angular';\nimport { IAugmentedJQuery, ITimeoutService, IScope, IInterpolateService } from 'angular';\n\nimport {\n  Obj,\n  extend,\n  forEach,\n  tail,\n  isString,\n  isObject,\n  isArray,\n  parse,\n  noop,\n  unnestR,\n  identity,\n  uniqR,\n  inArray,\n  removeFrom,\n  RawParams,\n  PathNode,\n  StateOrName,\n  StateService,\n  StateDeclaration,\n  UIRouter,\n} from '@uirouter/core';\nimport { UIViewData } from './viewDirective';\n\n/** @hidden Used for typedoc */\nexport interface ng1_directive {}\n\n/** @hidden */\nfunction parseStateRef(ref: string) {\n  const paramsOnly = ref.match(/^\\s*({[^}]*})\\s*$/);\n  if (paramsOnly) ref = '(' + paramsOnly[1] + ')';\n\n  const parsed = ref.replace(/\\n/g, ' ').match(/^\\s*([^(]*?)\\s*(\\((.*)\\))?\\s*$/);\n  if (!parsed || parsed.length !== 4) throw new Error(\"Invalid state ref '\" + ref + \"'\");\n  return { state: parsed[1] || null, paramExpr: parsed[3] || null };\n}\n\n/** @hidden */\nfunction stateContext(el: IAugmentedJQuery) {\n  const $uiView: UIViewData = (el.parent() as IAugmentedJQuery).inheritedData('$uiView');\n  const path: PathNode[] = parse('$cfg.path')($uiView);\n  return path ? tail(path).state.name : undefined;\n}\n\n/** @hidden */\nfunction processedDef($state: StateService, $element: IAugmentedJQuery, def: Def): Def {\n  const uiState = def.uiState || $state.current.name;\n  const uiStateOpts = extend(defaultOpts($element, $state), def.uiStateOpts || {});\n  const href = $state.href(uiState, def.uiStateParams, uiStateOpts);\n  return { uiState, uiStateParams: def.uiStateParams, uiStateOpts, href };\n}\n\n/** @hidden */\ninterface TypeInfo {\n  attr: string;\n  isAnchor: boolean;\n  clickable: boolean;\n}\n\n/** @hidden */\nfunction getTypeInfo(el: IAugmentedJQuery): TypeInfo {\n  // SVGAElement does not use the href attribute, but rather the 'xlinkHref' attribute.\n  const isSvg = Object.prototype.toString.call(el.prop('href')) === '[object SVGAnimatedString]';\n  const isForm = el[0].nodeName === 'FORM';\n\n  return {\n    attr: isForm ? 'action' : isSvg ? 'xlink:href' : 'href',\n    isAnchor: el.prop('tagName').toUpperCase() === 'A',\n    clickable: !isForm,\n  };\n}\n\n/** @hidden */\nfunction clickHook(\n  el: IAugmentedJQuery,\n  $state: StateService,\n  $timeout: ITimeoutService,\n  type: TypeInfo,\n  getDef: () => Def\n) {\n  return function (e: JQueryMouseEventObject) {\n    const button = e.which || e.button,\n      target = getDef();\n\n    if (!(button > 1 || e.ctrlKey || e.metaKey || e.shiftKey || e.altKey || el.attr('target'))) {\n      // HACK: This is to allow ng-clicks to be processed before the transition is initiated:\n      const transition = $timeout(function () {\n        if (!el.attr('disabled')) {\n          $state.go(target.uiState, target.uiStateParams, target.uiStateOpts);\n        }\n      });\n      e.preventDefault();\n\n      // if the state has no URL, ignore one preventDefault from the <a> directive.\n      let ignorePreventDefaultCount = type.isAnchor && !target.href ? 1 : 0;\n\n      e.preventDefault = function () {\n        if (ignorePreventDefaultCount-- <= 0) $timeout.cancel(transition);\n      };\n    }\n  };\n}\n\n/** @hidden */\nfunction defaultOpts(el: IAugmentedJQuery, $state: StateService) {\n  return {\n    relative: stateContext(el) || $state.$current,\n    inherit: true,\n    source: 'sref',\n  };\n}\n\n/** @hidden */\nfunction bindEvents(element: IAugmentedJQuery, scope: IScope, hookFn: EventListener, uiStateOpts: any): void {\n  let events;\n\n  if (uiStateOpts) {\n    events = uiStateOpts.events;\n  }\n\n  if (!isArray(events)) {\n    events = ['click'];\n  }\n\n  const on = element.on ? 'on' : 'bind';\n  for (const event of events) {\n    element[on](event, hookFn);\n  }\n\n  scope.$on('$destroy', function () {\n    const off = element.off ? 'off' : 'unbind';\n    for (const event of events) {\n      element[off](event, hookFn as any);\n    }\n  });\n}\n\n/**\n * `ui-sref`: A directive for linking to a state\n *\n * A directive which links to a state (and optionally, parameters).\n * When clicked, this directive activates the linked state with the supplied parameter values.\n *\n * ### Linked State\n * The attribute value of the `ui-sref` is the name of the state to link to.\n *\n * #### Example:\n * This will activate the `home` state when the link is clicked.\n * ```html\n * <a ui-sref=\"home\">Home</a>\n * ```\n *\n * ### Relative Links\n * You can also use relative state paths within `ui-sref`, just like a relative path passed to `$state.go()` ([[StateService.go]]).\n * You just need to be aware that the path is relative to the state that *created* the link.\n * This allows a state to create a relative `ui-sref` which always targets the same destination.\n *\n * #### Example:\n * Both these links are relative to the parent state, even when a child state is currently active.\n * ```html\n * <a ui-sref=\".child1\">child 1 state</a>\n * <a ui-sref=\".child2\">child 2 state</a>\n * ```\n *\n * This link activates the parent state.\n * ```html\n * <a ui-sref=\"^\">Return</a>\n * ```\n *\n * ### hrefs\n * If the linked state has a URL, the directive will automatically generate and\n * update the `href` attribute (using the [[StateService.href]]  method).\n *\n * #### Example:\n * Assuming the `users` state has a url of `/users/`\n * ```html\n * <a ui-sref=\"users\" href=\"/users/\">Users</a>\n * ```\n *\n * ### Parameter Values\n * In addition to the state name, a `ui-sref` can include parameter values which are applied when activating the state.\n * Param values can be provided in the `ui-sref` value after the state name, enclosed by parentheses.\n * The content inside the parentheses is an expression, evaluated to the parameter values.\n *\n * #### Example:\n * This example renders a list of links to users.\n * The state's `userId` parameter value comes from each user's `user.id` property.\n * ```html\n * <li ng-repeat=\"user in users\">\n *   <a ui-sref=\"users.detail({ userId: user.id })\">{{ user.displayName }}</a>\n * </li>\n * ```\n *\n * Note:\n * The parameter values expression is `$watch`ed for updates.\n *\n * ### Transition Options\n * You can specify [[TransitionOptions]] to pass to [[StateService.go]] by using the `ui-sref-opts` attribute.\n * Options are restricted to `location`, `inherit`, and `reload`.\n *\n * #### Example:\n * ```html\n * <a ui-sref=\"home\" ui-sref-opts=\"{ reload: true }\">Home</a>\n * ```\n *\n * ### Other DOM Events\n *\n * You can also customize which DOM events to respond to (instead of `click`) by\n * providing an `events` array in the `ui-sref-opts` attribute.\n *\n * #### Example:\n * ```html\n * <input type=\"text\" ui-sref=\"contacts\" ui-sref-opts=\"{ events: ['change', 'blur'] }\">\n * ```\n *\n * ### Highlighting the active link\n * This directive can be used in conjunction with [[uiSrefActive]] to highlight the active link.\n *\n * ### Examples\n * If you have the following template:\n *\n * ```html\n * <a ui-sref=\"home\">Home</a>\n * <a ui-sref=\"about\">About</a>\n * <a ui-sref=\"{page: 2}\">Next page</a>\n *\n * <ul>\n *     <li ng-repeat=\"contact in contacts\">\n *         <a ui-sref=\"contacts.detail({ id: contact.id })\">{{ contact.name }}</a>\n *     </li>\n * </ul>\n * ```\n *\n * Then (assuming the current state is `contacts`) the rendered html including hrefs would be:\n *\n * ```html\n * <a href=\"#/home\" ui-sref=\"home\">Home</a>\n * <a href=\"#/about\" ui-sref=\"about\">About</a>\n * <a href=\"#/contacts?page=2\" ui-sref=\"{page: 2}\">Next page</a>\n *\n * <ul>\n *     <li ng-repeat=\"contact in contacts\">\n *         <a href=\"#/contacts/1\" ui-sref=\"contacts.detail({ id: contact.id })\">Joe</a>\n *     </li>\n *     <li ng-repeat=\"contact in contacts\">\n *         <a href=\"#/contacts/2\" ui-sref=\"contacts.detail({ id: contact.id })\">Alice</a>\n *     </li>\n *     <li ng-repeat=\"contact in contacts\">\n *         <a href=\"#/contacts/3\" ui-sref=\"contacts.detail({ id: contact.id })\">Bob</a>\n *     </li>\n * </ul>\n *\n * <a href=\"#/home\" ui-sref=\"home\" ui-sref-opts=\"{reload: true}\">Home</a>\n * ```\n *\n * ### Notes\n *\n * - You can use `ui-sref` to change **only the parameter values** by omitting the state name and parentheses.\n * #### Example:\n * Sets the `lang` parameter to `en` and remains on the same state.\n *\n * ```html\n * <a ui-sref=\"{ lang: 'en' }\">English</a>\n * ```\n *\n * - A middle-click, right-click, or ctrl-click is handled (natively) by the browser to open the href in a new window, for example.\n *\n * - Unlike the parameter values expression, the state name is not `$watch`ed (for performance reasons).\n * If you need to dynamically update the state being linked to, use the fully dynamic [[uiState]] directive.\n */\nlet uiSrefDirective: ng1_directive;\nuiSrefDirective = [\n  '$uiRouter',\n  '$timeout',\n  function $StateRefDirective($uiRouter: UIRouter, $timeout: ITimeoutService) {\n    const $state = $uiRouter.stateService;\n\n    return {\n      restrict: 'A',\n      require: ['?^uiSrefActive', '?^uiSrefActiveEq'],\n      link: function (scope: IScope, element: IAugmentedJQuery, attrs: any, uiSrefActive: any) {\n        const type = getTypeInfo(element);\n        const active = uiSrefActive[1] || uiSrefActive[0];\n        let unlinkInfoFn: Function = null;\n\n        const rawDef = {} as Def;\n        const getDef = () => processedDef($state, element, rawDef);\n\n        const ref = parseStateRef(attrs.uiSref);\n        rawDef.uiState = ref.state;\n        rawDef.uiStateOpts = attrs.uiSrefOpts ? scope.$eval(attrs.uiSrefOpts) : {};\n\n        function update() {\n          const def = getDef();\n          if (unlinkInfoFn) unlinkInfoFn();\n          if (active) unlinkInfoFn = active.$$addStateInfo(def.uiState, def.uiStateParams);\n          if (def.href != null) attrs.$set(type.attr, def.href);\n        }\n\n        if (ref.paramExpr) {\n          scope.$watch(\n            ref.paramExpr,\n            function (val) {\n              rawDef.uiStateParams = extend({}, val);\n              update();\n            },\n            true\n          );\n          rawDef.uiStateParams = extend({}, scope.$eval(ref.paramExpr));\n        }\n\n        update();\n\n        scope.$on('$destroy', <any>$uiRouter.stateRegistry.onStatesChanged(update));\n        scope.$on('$destroy', <any>$uiRouter.transitionService.onSuccess({}, update));\n\n        if (!type.clickable) return;\n        const hookFn = clickHook(element, $state, $timeout, type, getDef);\n        bindEvents(element, scope, hookFn, rawDef.uiStateOpts);\n      },\n    };\n  },\n];\n\n/**\n * `ui-state`: A fully dynamic directive for linking to a state\n *\n * A directive which links to a state (and optionally, parameters).\n * When clicked, this directive activates the linked state with the supplied parameter values.\n *\n * **This directive is very similar to [[uiSref]], but it `$observe`s and `$watch`es/evaluates all its inputs.**\n *\n * A directive which links to a state (and optionally, parameters).\n * When clicked, this directive activates the linked state with the supplied parameter values.\n *\n * ### Linked State\n * The attribute value of `ui-state` is an expression which is `$watch`ed and evaluated as the state to link to.\n * **This is in contrast with `ui-sref`, which takes a state name as a string literal.**\n *\n * #### Example:\n * Create a list of links.\n * ```html\n * <li ng-repeat=\"link in navlinks\">\n *   <a ui-state=\"link.state\">{{ link.displayName }}</a>\n * </li>\n * ```\n *\n * ### Relative Links\n * If the expression evaluates to a relative path, it is processed like [[uiSref]].\n * You just need to be aware that the path is relative to the state that *created* the link.\n * This allows a state to create relative `ui-state` which always targets the same destination.\n *\n * ### hrefs\n * If the linked state has a URL, the directive will automatically generate and\n * update the `href` attribute (using the [[StateService.href]]  method).\n *\n * ### Parameter Values\n * In addition to the state name expression, a `ui-state` can include parameter values which are applied when activating the state.\n * Param values should be provided using the `ui-state-params` attribute.\n * The `ui-state-params` attribute value is `$watch`ed and evaluated as an expression.\n *\n * #### Example:\n * This example renders a list of links with param values.\n * The state's `userId` parameter value comes from each user's `user.id` property.\n * ```html\n * <li ng-repeat=\"link in navlinks\">\n *   <a ui-state=\"link.state\" ui-state-params=\"link.params\">{{ link.displayName }}</a>\n * </li>\n * ```\n *\n * ### Transition Options\n * You can specify [[TransitionOptions]] to pass to [[StateService.go]] by using the `ui-state-opts` attribute.\n * Options are restricted to `location`, `inherit`, and `reload`.\n * The value of the `ui-state-opts` is `$watch`ed and evaluated as an expression.\n *\n * #### Example:\n * ```html\n * <a ui-state=\"returnto.state\" ui-state-opts=\"{ reload: true }\">Home</a>\n * ```\n *\n * ### Other DOM Events\n *\n * You can also customize which DOM events to respond to (instead of `click`) by\n * providing an `events` array in the `ui-state-opts` attribute.\n *\n * #### Example:\n * ```html\n * <input type=\"text\" ui-state=\"contacts\" ui-state-opts=\"{ events: ['change', 'blur'] }\">\n * ```\n *\n * ### Highlighting the active link\n * This directive can be used in conjunction with [[uiSrefActive]] to highlight the active link.\n *\n * ### Notes\n *\n * - You can use `ui-params` to change **only the parameter values** by omitting the state name and supplying only `ui-state-params`.\n *   However, it might be simpler to use [[uiSref]] parameter-only links.\n *\n * #### Example:\n * Sets the `lang` parameter to `en` and remains on the same state.\n *\n * ```html\n * <a ui-state=\"\" ui-state-params=\"{ lang: 'en' }\">English</a>\n * ```\n *\n * - A middle-click, right-click, or ctrl-click is handled (natively) by the browser to open the href in a new window, for example.\n * ```\n */\nlet uiStateDirective: ng1_directive;\nuiStateDirective = [\n  '$uiRouter',\n  '$timeout',\n  function $StateRefDynamicDirective($uiRouter: UIRouter, $timeout: ITimeoutService) {\n    const $state = $uiRouter.stateService;\n\n    return {\n      restrict: 'A',\n      require: ['?^uiSrefActive', '?^uiSrefActiveEq'],\n      link: function (scope: IScope, element: IAugmentedJQuery, attrs: any, uiSrefActive: any) {\n        const type = getTypeInfo(element);\n        const active = uiSrefActive[1] || uiSrefActive[0];\n        let unlinkInfoFn: Function = null;\n        let hookFn;\n\n        const rawDef = {} as Def;\n        const getDef = () => processedDef($state, element, rawDef);\n\n        const inputAttrs = ['uiState', 'uiStateParams', 'uiStateOpts'];\n        const watchDeregFns = inputAttrs.reduce((acc, attr) => ((acc[attr] = noop), acc), {});\n\n        function update() {\n          const def = getDef();\n          if (unlinkInfoFn) unlinkInfoFn();\n          if (active) unlinkInfoFn = active.$$addStateInfo(def.uiState, def.uiStateParams);\n          if (def.href != null) attrs.$set(type.attr, def.href);\n        }\n\n        inputAttrs.forEach((field) => {\n          rawDef[field] = attrs[field] ? scope.$eval(attrs[field]) : null;\n\n          attrs.$observe(field, (expr) => {\n            watchDeregFns[field]();\n            watchDeregFns[field] = scope.$watch(\n              expr,\n              (newval) => {\n                rawDef[field] = newval;\n                update();\n              },\n              true\n            );\n          });\n        });\n\n        update();\n\n        scope.$on('$destroy', <any>$uiRouter.stateRegistry.onStatesChanged(update));\n        scope.$on('$destroy', <any>$uiRouter.transitionService.onSuccess({}, update));\n\n        if (!type.clickable) return;\n        hookFn = clickHook(element, $state, $timeout, type, getDef);\n        bindEvents(element, scope, hookFn, rawDef.uiStateOpts);\n      },\n    };\n  },\n];\n\n/**\n * `ui-sref-active` and `ui-sref-active-eq`: A directive that adds a CSS class when a `ui-sref` is active\n *\n * A directive working alongside [[uiSref]] and [[uiState]] to add classes to an element when the\n * related directive's state is active (and remove them when it is inactive).\n *\n * The primary use-case is to highlight the active link in navigation menus,\n * distinguishing it from the inactive menu items.\n *\n * ### Linking to a `ui-sref` or `ui-state`\n * `ui-sref-active` can live on the same element as `ui-sref`/`ui-state`, or it can be on a parent element.\n * If a `ui-sref-active` is a parent to more than one `ui-sref`/`ui-state`, it will apply the CSS class when **any of the links are active**.\n *\n * ### Matching\n *\n * The `ui-sref-active` directive applies the CSS class when the `ui-sref`/`ui-state`'s target state **or any child state is active**.\n * This is a \"fuzzy match\" which uses [[StateService.includes]].\n *\n * The `ui-sref-active-eq` directive applies the CSS class when the `ui-sref`/`ui-state`'s target state is directly active (not when child states are active).\n * This is an \"exact match\" which uses [[StateService.is]].\n *\n * ### Parameter values\n * If the `ui-sref`/`ui-state` includes parameter values, the current parameter values must match the link's values for the link to be highlighted.\n * This allows a list of links to the same state with different parameters to be rendered, and the correct one highlighted.\n *\n * #### Example:\n * ```html\n * <li ng-repeat=\"user in users\" ui-sref-active=\"active\">\n *   <a ui-sref=\"user.details({ userId: user.id })\">{{ user.lastName }}</a>\n * </li>\n * ```\n *\n * ### Examples\n *\n * Given the following template:\n * #### Example:\n * ```html\n * <ul>\n *   <li ui-sref-active=\"active\" class=\"item\">\n *     <a href ui-sref=\"app.user({user: 'bilbobaggins'})\">@bilbobaggins</a>\n *   </li>\n * </ul>\n * ```\n *\n * When the app state is `app.user` (or any child state),\n * and contains the state parameter \"user\" with value \"bilbobaggins\",\n * the resulting HTML will appear as (note the 'active' class):\n *\n * ```html\n * <ul>\n *   <li ui-sref-active=\"active\" class=\"item active\">\n *     <a ui-sref=\"app.user({user: 'bilbobaggins'})\" href=\"/users/bilbobaggins\">@bilbobaggins</a>\n *   </li>\n * </ul>\n * ```\n *\n * ### Glob mode\n *\n * It is possible to pass `ui-sref-active` an expression that evaluates to an object.\n * The objects keys represent active class names and values represent the respective state names/globs.\n * `ui-sref-active` will match if the current active state **includes** any of\n * the specified state names/globs, even the abstract ones.\n *\n * #### Example:\n * Given the following template, with \"admin\" being an abstract state:\n * ```html\n * <div ui-sref-active=\"{'active': 'admin.**'}\">\n *   <a ui-sref-active=\"active\" ui-sref=\"admin.roles\">Roles</a>\n * </div>\n * ```\n *\n * Arrays are also supported as values in the `ngClass`-like interface.\n * This allows multiple states to add `active` class.\n *\n * #### Example:\n * Given the following template, with \"admin.roles\" being the current state, the class will be added too:\n * ```html\n * <div ui-sref-active=\"{'active': ['owner.**', 'admin.**']}\">\n *   <a ui-sref-active=\"active\" ui-sref=\"admin.roles\">Roles</a>\n * </div>\n * ```\n *\n * When the current state is \"admin.roles\" the \"active\" class will be applied to both the `<div>` and `<a>` elements.\n * It is important to note that the state names/globs passed to `ui-sref-active` override any state provided by a linked `ui-sref`.\n *\n * ### Notes:\n *\n * - The class name is interpolated **once** during the directives link time (any further changes to the\n * interpolated value are ignored).\n *\n * - Multiple classes may be specified in a space-separated format: `ui-sref-active='class1 class2 class3'`\n */\nlet uiSrefActiveDirective: ng1_directive;\nuiSrefActiveDirective = [\n  '$state',\n  '$stateParams',\n  '$interpolate',\n  '$uiRouter',\n  function $StateRefActiveDirective(\n    $state: StateService,\n    $stateParams: Obj,\n    $interpolate: IInterpolateService,\n    $uiRouter: UIRouter\n  ) {\n    return {\n      restrict: 'A',\n      controller: [\n        '$scope',\n        '$element',\n        '$attrs',\n        function ($scope: IScope, $element: IAugmentedJQuery, $attrs: any) {\n          let states: StateData[] = [];\n          let activeEqClass: string;\n          let uiSrefActive: any;\n\n          // There probably isn't much point in $observing this\n          // uiSrefActive and uiSrefActiveEq share the same directive object with some\n          // slight difference in logic routing\n          activeEqClass = $interpolate($attrs.uiSrefActiveEq || '', false)($scope);\n\n          try {\n            uiSrefActive = $scope.$eval($attrs.uiSrefActive);\n          } catch (e) {\n            // Do nothing. uiSrefActive is not a valid expression.\n            // Fall back to using $interpolate below\n          }\n          uiSrefActive = uiSrefActive || $interpolate($attrs.uiSrefActive || '', false)($scope);\n          setStatesFromDefinitionObject(uiSrefActive);\n\n          // Allow uiSref to communicate with uiSrefActive[Equals]\n          this.$$addStateInfo = function (newState: string, newParams: Obj) {\n            // we already got an explicit state provided by ui-sref-active, so we\n            // shadow the one that comes from ui-sref\n            if (isObject(uiSrefActive) && states.length > 0) {\n              return;\n            }\n            const deregister = addState(newState, newParams, uiSrefActive);\n            update();\n            return deregister;\n          };\n\n          function updateAfterTransition(trans) {\n            trans.promise.then(update, noop);\n          }\n          $scope.$on('$destroy', setupEventListeners());\n          if ($uiRouter.globals.transition) {\n            updateAfterTransition($uiRouter.globals.transition);\n          }\n\n          function setupEventListeners() {\n            const deregisterStatesChangedListener = $uiRouter.stateRegistry.onStatesChanged(handleStatesChanged);\n            const deregisterOnStartListener = $uiRouter.transitionService.onStart({}, updateAfterTransition);\n            const deregisterStateChangeSuccessListener = $scope.$on('$stateChangeSuccess', update);\n            return function cleanUp() {\n              deregisterStatesChangedListener();\n              deregisterOnStartListener();\n              deregisterStateChangeSuccessListener();\n            };\n          }\n\n          function handleStatesChanged() {\n            setStatesFromDefinitionObject(uiSrefActive);\n          }\n\n          function setStatesFromDefinitionObject(statesDefinition: Obj) {\n            if (isObject(statesDefinition)) {\n              states = [];\n              forEach(statesDefinition, function (stateOrName: StateOrName | Array<StateOrName>, activeClass: string) {\n                // Helper function to abstract adding state.\n                const addStateForClass = function (stateOrName: string, activeClass: string) {\n                  const ref = parseStateRef(stateOrName);\n                  addState(ref.state, $scope.$eval(ref.paramExpr), activeClass);\n                };\n\n                if (isString(stateOrName)) {\n                  // If state is string, just add it.\n                  addStateForClass(stateOrName as string, activeClass);\n                } else if (isArray(stateOrName)) {\n                  // If state is an array, iterate over it and add each array item individually.\n                  forEach(stateOrName, function (stateOrName: string) {\n                    addStateForClass(stateOrName, activeClass);\n                  });\n                }\n              });\n            }\n          }\n\n          function addState(stateName: string, stateParams: Obj, activeClass: string) {\n            const state = $state.get(stateName, stateContext($element));\n\n            const stateInfo = {\n              state: state || { name: stateName },\n              params: stateParams,\n              activeClass: activeClass,\n            };\n\n            states.push(stateInfo);\n\n            return function removeState() {\n              removeFrom(states)(stateInfo);\n            };\n          }\n\n          // Update route state\n          function update() {\n            const splitClasses = (str) => str.split(/\\s/).filter(identity);\n            const getClasses = (stateList: StateData[]) =>\n              stateList\n                .map((x) => x.activeClass)\n                .map(splitClasses)\n                .reduce(unnestR, []);\n\n            const allClasses = getClasses(states).concat(splitClasses(activeEqClass)).reduce(uniqR, []);\n            const fuzzyClasses = getClasses(states.filter((x) => $state.includes(x.state.name, x.params)));\n            const exactlyMatchesAny = !!states.filter((x) => $state.is(x.state.name, x.params)).length;\n            const exactClasses = exactlyMatchesAny ? splitClasses(activeEqClass) : [];\n\n            const addClasses = fuzzyClasses.concat(exactClasses).reduce(uniqR, []);\n            const removeClasses = allClasses.filter((cls) => !inArray(addClasses, cls));\n\n            $scope.$evalAsync(() => {\n              addClasses.forEach((className) => $element.addClass(className));\n              removeClasses.forEach((className) => $element.removeClass(className));\n            });\n          }\n\n          update();\n        },\n      ],\n    };\n  },\n];\n\n/** @hidden */\ninterface Def {\n  uiState: string;\n  href: string;\n  uiStateParams: Obj;\n  uiStateOpts: any;\n}\n/** @hidden */\ninterface StateData {\n  state: StateDeclaration;\n  params: RawParams;\n  activeClass: string;\n}\n\nangular\n  .module('ui.router.state')\n  .directive('uiSref', uiSrefDirective)\n  .directive('uiSrefActive', uiSrefActiveDirective)\n  .directive('uiSrefActiveEq', uiSrefActiveDirective)\n  .directive('uiState', uiStateDirective);\n", "/** @publicapi @module ng1 */ /** */\n\nimport { ng as angular } from './angular';\nimport { Obj, StateService, StateOrName } from '@uirouter/core';\n\n/**\n * `isState` Filter: truthy if the current state is the parameter\n *\n * Translates to [[StateService.is]] `$state.is(\"stateName\")`.\n *\n * #### Example:\n * ```html\n * <div ng-if=\"'stateName' | isState\">show if state is 'stateName'</div>\n * ```\n */\n$IsStateFilter.$inject = ['$state'];\nfunction $IsStateFilter($state: StateService) {\n  const isFilter: any = function (state: StateOrName, params: Obj, options?: { relative?: StateOrName }) {\n    return $state.is(state, params, options);\n  };\n  isFilter.$stateful = true;\n  return isFilter;\n}\n\n/**\n * `includedByState` Filter: truthy if the current state includes the parameter\n *\n * Translates to [[StateService.includes]]` $state.is(\"fullOrPartialStateName\")`.\n *\n * #### Example:\n * ```html\n * <div ng-if=\"'fullOrPartialStateName' | includedByState\">show if state includes 'fullOrPartialStateName'</div>\n * ```\n */\n$IncludedByStateFilter.$inject = ['$state'];\nfunction $IncludedByStateFilter($state: StateService) {\n  const includesFilter: any = function (state: StateOrName, params: Obj, options: { relative?: StateOrName }) {\n    return $state.includes(state, params, options);\n  };\n  includesFilter.$stateful = true;\n  return includesFilter;\n}\n\nangular.module('ui.router.state').filter('isState', $IsStateFilter).filter('includedByState', $IncludedByStateFilter);\n\nexport { $IsStateFilter, $IncludedByStateFilter };\n", "/** @publicapi @module directives */ /** */\nimport {\n  $QLike,\n  ActiveUIView,\n  extend,\n  filter,\n  HookRegOptions,\n  isDefined,\n  isFunction,\n  isString,\n  kebobString,\n  noop,\n  Obj,\n  Param,\n  parse,\n  PathNode,\n  ResolveContext,\n  StateDeclaration,\n  tail,\n  trace,\n  Transition,\n  TransitionService,\n  TypedMap,\n  unnestR,\n  ViewService,\n} from '@uirouter/core';\nimport { IAugmentedJQuery, IInterpolateService, IScope, ITranscludeFunction } from 'angular';\nimport { ng as angular } from '../angular';\nimport { Ng1Controller, Ng1StateDeclaration } from '../interface';\nimport { getLocals } from '../services';\nimport { Ng1ViewConfig } from '../statebuilders/views';\nimport { ng1_directive } from './stateDirectives';\n\n/** @hidden */\nexport type UIViewData = {\n  $cfg: Ng1ViewConfig;\n  $uiView: ActiveUIView;\n};\n\n/** @hidden */\nexport type UIViewAnimData = {\n  $animEnter: Promise<any>;\n  $animLeave: Promise<any>;\n  $$animLeave: { resolve: () => any }; // \"deferred\"\n};\n\n/**\n * `ui-view`: A viewport directive which is filled in by a view from the active state.\n *\n * ### Attributes\n *\n * - `name`: (Optional) A view name.\n *   The name should be unique amongst the other views in the same state.\n *   You can have views of the same name that live in different states.\n *   The ui-view can be targeted in a View using the name ([[Ng1StateDeclaration.views]]).\n *\n * - `autoscroll`: an expression. When it evaluates to true, the `ui-view` will be scrolled into view when it is activated.\n *   Uses [[$uiViewScroll]] to do the scrolling.\n *\n * - `onload`: Expression to evaluate whenever the view updates.\n *\n * #### Example:\n * A view can be unnamed or named.\n * ```html\n * <!-- Unnamed -->\n * <div ui-view></div>\n *\n * <!-- Named -->\n * <div ui-view=\"viewName\"></div>\n *\n * <!-- Named (different style) -->\n * <ui-view name=\"viewName\"></ui-view>\n * ```\n *\n * You can only have one unnamed view within any template (or root html). If you are only using a\n * single view and it is unnamed then you can populate it like so:\n *\n * ```html\n * <div ui-view></div>\n * $stateProvider.state(\"home\", {\n *   template: \"<h1>HELLO!</h1>\"\n * })\n * ```\n *\n * The above is a convenient shortcut equivalent to specifying your view explicitly with the\n * [[Ng1StateDeclaration.views]] config property, by name, in this case an empty name:\n *\n * ```js\n * $stateProvider.state(\"home\", {\n *   views: {\n *     \"\": {\n *       template: \"<h1>HELLO!</h1>\"\n *     }\n *   }\n * })\n * ```\n *\n * But typically you'll only use the views property if you name your view or have more than one view\n * in the same template. There's not really a compelling reason to name a view if its the only one,\n * but you could if you wanted, like so:\n *\n * ```html\n * <div ui-view=\"main\"></div>\n * ```\n *\n * ```js\n * $stateProvider.state(\"home\", {\n *   views: {\n *     \"main\": {\n *       template: \"<h1>HELLO!</h1>\"\n *     }\n *   }\n * })\n * ```\n *\n * Really though, you'll use views to set up multiple views:\n *\n * ```html\n * <div ui-view></div>\n * <div ui-view=\"chart\"></div>\n * <div ui-view=\"data\"></div>\n * ```\n *\n * ```js\n * $stateProvider.state(\"home\", {\n *   views: {\n *     \"\": {\n *       template: \"<h1>HELLO!</h1>\"\n *     },\n *     \"chart\": {\n *       template: \"<chart_thing/>\"\n *     },\n *     \"data\": {\n *       template: \"<data_thing/>\"\n *     }\n *   }\n * })\n * ```\n *\n * #### Examples for `autoscroll`:\n * ```html\n * <!-- If autoscroll present with no expression,\n *      then scroll ui-view into view -->\n * <ui-view autoscroll/>\n *\n * <!-- If autoscroll present with valid expression,\n *      then scroll ui-view into view if expression evaluates to true -->\n * <ui-view autoscroll='true'/>\n * <ui-view autoscroll='false'/>\n * <ui-view autoscroll='scopeVariable'/>\n * ```\n *\n * Resolve data:\n *\n * The resolved data from the state's `resolve` block is placed on the scope as `$resolve` (this\n * can be customized using [[Ng1ViewDeclaration.resolveAs]]).  This can be then accessed from the template.\n *\n * Note that when `controllerAs` is being used, `$resolve` is set on the controller instance *after* the\n * controller is instantiated.  The `$onInit()` hook can be used to perform initialization code which\n * depends on `$resolve` data.\n *\n * #### Example:\n * ```js\n * $stateProvider.state('home', {\n *   template: '<my-component user=\"$resolve.user\"></my-component>',\n *   resolve: {\n *     user: function(UserService) { return UserService.fetchUser(); }\n *   }\n * });\n * ```\n */\nexport let uiView: ng1_directive;\n// eslint-disable-next-line prefer-const\nuiView = [\n  '$view',\n  '$animate',\n  '$uiViewScroll',\n  '$interpolate',\n  '$q',\n  function $ViewDirective(\n    $view: ViewService,\n    $animate: any,\n    $uiViewScroll: any,\n    $interpolate: IInterpolateService,\n    $q: $QLike\n  ) {\n    function getRenderer() {\n      return {\n        enter: function (element: JQuery, target: any, cb: Function) {\n          if (angular.version.minor > 2) {\n            $animate.enter(element, null, target).then(cb);\n          } else {\n            $animate.enter(element, null, target, cb);\n          }\n        },\n        leave: function (element: JQuery, cb: Function) {\n          if (angular.version.minor > 2) {\n            $animate.leave(element).then(cb);\n          } else {\n            $animate.leave(element, cb);\n          }\n        },\n      };\n    }\n\n    function configsEqual(config1: Ng1ViewConfig, config2: Ng1ViewConfig) {\n      return config1 === config2;\n    }\n\n    const rootData = {\n      $cfg: { viewDecl: { $context: $view._pluginapi._rootViewContext() } },\n      $uiView: {},\n    };\n\n    const directive = {\n      count: 0,\n      restrict: 'ECA',\n      terminal: true,\n      priority: 400,\n      transclude: 'element',\n      compile: function (tElement: JQuery, tAttrs: Obj, $transclude: ITranscludeFunction) {\n        return function (scope: IScope, $element: IAugmentedJQuery, attrs: Obj) {\n          const onloadExp = attrs['onload'] || '',\n            autoScrollExp = attrs['autoscroll'],\n            renderer = getRenderer(),\n            inherited = $element.inheritedData('$uiView') || rootData,\n            name = $interpolate(attrs['uiView'] || attrs['name'] || '')(scope) || '$default';\n\n          let previousEl: JQuery, currentEl: JQuery, currentScope: IScope, viewConfig: Ng1ViewConfig;\n\n          const activeUIView: ActiveUIView = {\n            $type: 'ng1',\n            id: directive.count++, // Global sequential ID for ui-view tags added to DOM\n            name: name, // ui-view name (<div ui-view=\"name\"></div>\n            fqn: inherited.$uiView.fqn ? inherited.$uiView.fqn + '.' + name : name, // fully qualified name, describes location in DOM\n            config: null, // The ViewConfig loaded (from a state.views definition)\n            configUpdated: configUpdatedCallback, // Called when the matching ViewConfig changes\n            get creationContext() {\n              // The context in which this ui-view \"tag\" was created\n              const fromParentTagConfig = parse('$cfg.viewDecl.$context')(inherited);\n              // Allow <ui-view name=\"foo\"><ui-view name=\"bar\"></ui-view></ui-view>\n              // See https://github.com/angular-ui/ui-router/issues/3355\n              const fromParentTag = parse('$uiView.creationContext')(inherited);\n              return fromParentTagConfig || fromParentTag;\n            },\n          };\n\n          trace.traceUIViewEvent('Linking', activeUIView);\n\n          function configUpdatedCallback(config?: Ng1ViewConfig) {\n            if (config && !(config instanceof Ng1ViewConfig)) return;\n            if (configsEqual(viewConfig, config)) return;\n            trace.traceUIViewConfigUpdated(activeUIView, config && config.viewDecl && config.viewDecl.$context);\n\n            viewConfig = config;\n            updateView(config);\n          }\n\n          $element.data('$uiView', { $uiView: activeUIView });\n\n          updateView();\n\n          const unregister = $view.registerUIView(activeUIView);\n          scope.$on('$destroy', function () {\n            trace.traceUIViewEvent('Destroying/Unregistering', activeUIView);\n            unregister();\n          });\n\n          function cleanupLastView() {\n            if (previousEl) {\n              trace.traceUIViewEvent('Removing (previous) el', previousEl.data('$uiView'));\n              previousEl.remove();\n              previousEl = null;\n            }\n\n            if (currentScope) {\n              trace.traceUIViewEvent('Destroying scope', activeUIView);\n              currentScope.$destroy();\n              currentScope = null;\n            }\n\n            if (currentEl) {\n              const _viewData = currentEl.data('$uiViewAnim');\n              trace.traceUIViewEvent('Animate out', _viewData);\n              renderer.leave(currentEl, function () {\n                _viewData.$$animLeave.resolve();\n                previousEl = null;\n              });\n\n              previousEl = currentEl;\n              currentEl = null;\n            }\n          }\n\n          function updateView(config?: Ng1ViewConfig) {\n            const newScope = scope.$new();\n            const animEnter = $q.defer(),\n              animLeave = $q.defer();\n\n            const $uiViewData: UIViewData = {\n              $cfg: config,\n              $uiView: activeUIView,\n            };\n\n            const $uiViewAnim: UIViewAnimData = {\n              $animEnter: animEnter.promise,\n              $animLeave: animLeave.promise,\n              $$animLeave: animLeave,\n            };\n\n            /**\n             * @ngdoc event\n             * @name ui.router.state.directive:ui-view#$viewContentLoading\n             * @eventOf ui.router.state.directive:ui-view\n             * @eventType emits on ui-view directive scope\n             * @description\n             *\n             * Fired once the view **begins loading**, *before* the DOM is rendered.\n             *\n             * @param {Object} event Event object.\n             * @param {string} viewName Name of the view.\n             */\n            newScope.$emit('$viewContentLoading', name);\n\n            const cloned = $transclude(newScope, function (clone) {\n              clone.data('$uiViewAnim', $uiViewAnim);\n              clone.data('$uiView', $uiViewData);\n              renderer.enter(clone, $element, function onUIViewEnter() {\n                animEnter.resolve();\n                if (currentScope) currentScope.$emit('$viewContentAnimationEnded');\n\n                if ((isDefined(autoScrollExp) && !autoScrollExp) || scope.$eval(autoScrollExp)) {\n                  $uiViewScroll(clone);\n                }\n              });\n\n              cleanupLastView();\n            });\n\n            currentEl = cloned;\n            currentScope = newScope;\n            /**\n             * @ngdoc event\n             * @name ui.router.state.directive:ui-view#$viewContentLoaded\n             * @eventOf ui.router.state.directive:ui-view\n             * @eventType emits on ui-view directive scope\n             * @description           *\n             * Fired once the view is **loaded**, *after* the DOM is rendered.\n             *\n             * @param {Object} event Event object.\n             */\n            currentScope.$emit('$viewContentLoaded', config || viewConfig);\n            currentScope.$eval(onloadExp);\n          }\n        };\n      },\n    };\n\n    return directive;\n  },\n];\n\n$ViewDirectiveFill.$inject = ['$compile', '$controller', '$transitions', '$view', '$q'];\n\n/** @hidden */\nfunction $ViewDirectiveFill(\n  $compile: angular.ICompileService,\n  $controller: angular.IControllerService,\n  $transitions: TransitionService,\n  $view: ViewService,\n  $q: angular.IQService\n) {\n  const getControllerAs = parse('viewDecl.controllerAs');\n  const getResolveAs = parse('viewDecl.resolveAs');\n\n  return {\n    restrict: 'ECA',\n    priority: -400,\n    compile: function (tElement: JQuery) {\n      const initial = tElement.html();\n      tElement.empty();\n\n      return function (scope: IScope, $element: JQuery) {\n        const data: UIViewData = $element.data('$uiView');\n        if (!data) {\n          $element.html(initial);\n          $compile($element.contents() as any)(scope);\n          return;\n        }\n\n        const cfg: Ng1ViewConfig = data.$cfg || <any>{ viewDecl: {}, getTemplate: noop };\n        const resolveCtx: ResolveContext = cfg.path && new ResolveContext(cfg.path);\n        $element.html(cfg.getTemplate($element, resolveCtx) || initial);\n        trace.traceUIViewFill(data.$uiView, $element.html());\n\n        const link = $compile($element.contents() as any);\n        const controller = cfg.controller as angular.IControllerService;\n        const controllerAs: string = getControllerAs(cfg);\n        const resolveAs: string = getResolveAs(cfg);\n        const locals = resolveCtx && getLocals(resolveCtx);\n\n        scope[resolveAs] = locals;\n\n        if (controller) {\n          const controllerInstance = <Ng1Controller>(\n            $controller(controller, extend({}, locals, { $scope: scope, $element: $element }))\n          );\n          if (controllerAs) {\n            scope[controllerAs] = controllerInstance;\n            scope[controllerAs][resolveAs] = locals;\n          }\n\n          // TODO: Use $view service as a central point for registering component-level hooks\n          // Then, when a component is created, tell the $view service, so it can invoke hooks\n          // $view.componentLoaded(controllerInstance, { $scope: scope, $element: $element });\n          // scope.$on('$destroy', () => $view.componentUnloaded(controllerInstance, { $scope: scope, $element: $element }));\n\n          $element.data('$ngControllerController', controllerInstance);\n          $element.children().data('$ngControllerController', controllerInstance);\n\n          registerControllerCallbacks($q, $transitions, controllerInstance, scope, cfg);\n        }\n\n        // Wait for the component to appear in the DOM\n        if (isString(cfg.component)) {\n          const kebobName = kebobString(cfg.component);\n          const tagRegexp = new RegExp(`^(x-|data-)?${kebobName}$`, 'i');\n\n          const getComponentController = () => {\n            const directiveEl = [].slice\n              .call($element[0].children)\n              .filter((el: Element) => el && el.tagName && tagRegexp.exec(el.tagName));\n\n            return directiveEl && angular.element(directiveEl).data(`$${cfg.component}Controller`);\n          };\n\n          const deregisterWatch = scope.$watch(getComponentController, function (ctrlInstance) {\n            if (!ctrlInstance) return;\n            registerControllerCallbacks($q, $transitions, ctrlInstance, scope, cfg);\n            deregisterWatch();\n          });\n        }\n\n        link(scope);\n      };\n    },\n  };\n}\n\n/** @hidden */\nconst hasComponentImpl = typeof (angular as any).module('ui.router')['component'] === 'function';\n/** @hidden incrementing id */\nlet _uiCanExitId = 0;\n\n/** @hidden TODO: move these callbacks to $view and/or `/hooks/components.ts` or something */\nfunction registerControllerCallbacks(\n  $q: angular.IQService,\n  $transitions: TransitionService,\n  controllerInstance: Ng1Controller,\n  $scope: IScope,\n  cfg: Ng1ViewConfig\n) {\n  // Call $onInit() ASAP\n  if (\n    isFunction(controllerInstance.$onInit) &&\n    !((cfg.viewDecl.component || cfg.viewDecl.componentProvider) && hasComponentImpl)\n  ) {\n    controllerInstance.$onInit();\n  }\n\n  const viewState: Ng1StateDeclaration = tail(cfg.path).state.self;\n\n  const hookOptions: HookRegOptions = { bind: controllerInstance };\n  // Add component-level hook for onUiParamsChanged\n  if (isFunction(controllerInstance.uiOnParamsChanged)) {\n    const resolveContext: ResolveContext = new ResolveContext(cfg.path);\n    const viewCreationTrans = resolveContext.getResolvable('$transition$').data;\n\n    // Fire callback on any successful transition\n    const paramsUpdated = ($transition$: Transition) => {\n      // Exit early if the $transition$ is the same as the view was created within.\n      // Exit early if the $transition$ will exit the state the view is for.\n      if ($transition$ === viewCreationTrans || $transition$.exiting().indexOf(viewState as StateDeclaration) !== -1)\n        return;\n\n      const toParams = $transition$.params('to') as TypedMap<any>;\n      const fromParams = $transition$.params<TypedMap<any>>('from') as TypedMap<any>;\n      const getNodeSchema = (node: PathNode) => node.paramSchema;\n      const toSchema: Param[] = $transition$.treeChanges('to').map(getNodeSchema).reduce(unnestR, []);\n      const fromSchema: Param[] = $transition$.treeChanges('from').map(getNodeSchema).reduce(unnestR, []);\n\n      // Find the to params that have different values than the from params\n      const changedToParams = toSchema.filter((param: Param) => {\n        const idx = fromSchema.indexOf(param);\n        return idx === -1 || !fromSchema[idx].type.equals(toParams[param.id], fromParams[param.id]);\n      });\n\n      // Only trigger callback if a to param has changed or is new\n      if (changedToParams.length) {\n        const changedKeys: string[] = changedToParams.map((x) => x.id);\n        // Filter the params to only changed/new to params.  `$transition$.params()` may be used to get all params.\n        const newValues = filter(toParams, (val, key) => changedKeys.indexOf(key) !== -1);\n        controllerInstance.uiOnParamsChanged(newValues, $transition$);\n      }\n    };\n    $scope.$on('$destroy', <any>$transitions.onSuccess({}, paramsUpdated, hookOptions));\n  }\n\n  // Add component-level hook for uiCanExit\n  if (isFunction(controllerInstance.uiCanExit)) {\n    const id = _uiCanExitId++;\n    const cacheProp = '_uiCanExitIds';\n\n    // Returns true if a redirect transition already answered truthy\n    const prevTruthyAnswer = (trans: Transition) =>\n      !!trans && ((trans[cacheProp] && trans[cacheProp][id] === true) || prevTruthyAnswer(trans.redirectedFrom()));\n\n    // If a user answered yes, but the transition was later redirected, don't also ask for the new redirect transition\n    const wrappedHook = (trans: Transition) => {\n      let promise;\n      const ids = (trans[cacheProp] = trans[cacheProp] || {});\n\n      if (!prevTruthyAnswer(trans)) {\n        promise = $q.when(controllerInstance.uiCanExit(trans));\n        promise.then((val) => (ids[id] = val !== false));\n      }\n      return promise;\n    };\n\n    const criteria = { exiting: viewState.name };\n    $scope.$on('$destroy', <any>$transitions.onBefore(criteria, wrappedHook, hookOptions));\n  }\n}\n\nangular.module('ui.router.state').directive('uiView', <any>uiView);\nangular.module('ui.router.state').directive('uiView', <any>$ViewDirectiveFill);\n", "/** @publicapi @module ng1 */ /** */\nimport { ng as angular } from './angular';\nimport { IServiceProviderFactory } from 'angular';\nimport IAnchorScrollService = angular.IAnchorScrollService;\nimport ITimeoutService = angular.ITimeoutService;\n\nexport interface UIViewScrollProvider {\n  /**\n   * Uses standard anchorScroll behavior\n   *\n   * Reverts [[$uiViewScroll]] back to using the core [`$anchorScroll`](http://docs.angularjs.org/api/ng.$anchorScroll)\n   * service for scrolling based on the url anchor.\n   */\n  useAnchorScroll(): void;\n}\n\n/** @hidden */\nfunction $ViewScrollProvider() {\n  let useAnchorScroll = false;\n\n  this.useAnchorScroll = function () {\n    useAnchorScroll = true;\n  };\n\n  this.$get = [\n    '$anchorScroll',\n    '$timeout',\n    function ($anchorScroll: IAnchorScrollService, $timeout: ITimeoutService): Function {\n      if (useAnchorScroll) {\n        return $anchorScroll;\n      }\n\n      return function ($element: JQuery) {\n        return $timeout(\n          function () {\n            $element[0].scrollIntoView();\n          },\n          0,\n          false\n        );\n      };\n    },\n  ];\n}\n\nangular.module('ui.router.state').provider('$uiViewScroll', <IServiceProviderFactory>$ViewScrollProvider);\n", "/**\n * Main entry point for angular 1.x build\n * @publicapi @module ng1\n */ /** */\nexport * from './interface';\nexport * from './services';\nexport * from './statebuilders/views';\nexport * from './stateProvider';\nexport * from './urlRouterProvider';\n\nimport './injectables';\nimport './directives/stateDirectives';\nimport './stateFilters';\nimport './directives/viewDirective';\nimport './viewScroll';\n\nexport default 'ui.router';\n\nimport * as core from '@uirouter/core';\nexport { core };\nexport * from '@uirouter/core';\n"], "names": ["ng_from_import.module", "services", "isDefined", "pick", "for<PERSON>ach", "isString", "extend", "ViewService", "ResolveContext", "trace", "isInjectable", "isArray", "tail", "Resolvable", "angular", "isFunction", "kebobString", "unnestR", "isObject", "createProxyFunctions", "val", "removeFrom", "BaseUrlRule", "identity", "U<PERSON><PERSON><PERSON>", "applyPairs", "parse", "noop", "uniqR", "inArray", "filter"], "mappings": ";;;;;;;;;;;;;IAAA;IAGA,eAAe,IAAM,cAAc,GAAG,OAAO,CAAC;IAC9C,eAAsB,IAAM,EAAE,GAAG,cAAc,IAAIA,qBAAqB,GAAG,cAAc,GAAG,cAAc;;ICJ1G;IAwBA;aACgB,uBAAuB;QACrC,IAAI,eAAe,GAAoB,IAAI,CAAC;QAC5C,OAAO,UAAC,IAAI,EAAE,IAAI;YAChB,eAAe,GAAG,eAAe,IAAIC,aAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAChF,OAAO,CAAC,IAAI,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC;SACzD,CAAC;IACJ,CAAC;IAED;IACA,IAAM,SAAS,GAAG,UAAC,IAAI,EAAE,GAAG,IAAK,OAAA,IAAI,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,GAAG,IAAK,OAAA,GAAG,IAAIC,cAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAA,EAAE,KAAK,CAAC,GAAA,CAAC;IAE9F;;;;;;;;;;;aAWgB,eAAe,CAAC,KAAkB;;QAEhD,IAAI,CAAC,KAAK,CAAC,MAAM;YAAE,OAAO,EAAE,CAAC;QAE7B,IAAM,OAAO,GAAG,CAAC,kBAAkB,EAAE,aAAa,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,EAChF,QAAQ,GAAG,CAAC,YAAY,EAAE,oBAAoB,EAAE,cAAc,EAAE,WAAW,CAAC,EAC5E,QAAQ,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,mBAAmB,CAAC,EACzD,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,EACtC,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;;;;QAK7C,IAAIA,cAAS,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,EAAE;YAC3D,MAAM,IAAI,KAAK,CACb,YAAU,KAAK,CAAC,IAAI,6BAA0B;gBAC5C,+DAA6D;gBAC7D,qEAAqE;iBACrE,MAAI,WAAW,CAAC,MAAM,CAAC,UAAC,GAAG,IAAK,OAAAA,cAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAA,CAAC,CAAC,IAAI,CAAC,IAAI,CAAG,CAAA,CACtE,CAAC;SACH;QAED,IAAM,KAAK,GAA0C,EAAE,EACrD,WAAW,GAAG,KAAK,CAAC,KAAK,IAAI,EAAE,QAAQ,EAAEC,SAAI,CAAC,KAAK,EAAE,WAAW,CAAC,EAAE,CAAC;QAEtEC,YAAO,CAAC,WAAW,EAAE,UAAU,MAA0B,EAAE,IAAY;;YAErE,IAAI,GAAG,IAAI,IAAI,UAAU,CAAC;;YAE1B,IAAIC,aAAQ,CAAC,MAAM,CAAC;gBAAE,MAAM,GAAG,EAAE,SAAS,EAAU,MAAM,EAAE,CAAC;;YAG7D,MAAM,GAAGC,WAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;;YAG5B,IAAI,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,EAAE;gBACjE,MAAM,IAAI,KAAK,CACb,qBAAmB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,eAAU,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,wBAAmB,IAAI,SAAI,KAAK,CAAC,IAAI,MAAG,CAC7G,CAAC;aACH;YAED,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,UAAU,CAAC;YAClD,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;YACrB,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC;YACxB,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;YAEpB,IAAM,UAAU,GAAGC,gBAAW,CAAC,qBAAqB,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;YACpF,MAAM,CAAC,WAAW,GAAG,UAAU,CAAC,UAAU,CAAC;YAC3C,MAAM,CAAC,oBAAoB,GAAG,UAAU,CAAC,mBAAmB,CAAC;YAE7D,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;SACtB,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACf,CAAC;IAED;IACA,IAAI,EAAE,GAAG,CAAC,CAAC;IAEX;;QASE,uBAAmB,IAAgB,EAAS,QAA4B,EAAS,OAAwB;YAAzG,iBAA6G;YAA1F,SAAI,GAAJ,IAAI,CAAY;YAAS,aAAQ,GAAR,QAAQ,CAAoB;YAAS,YAAO,GAAP,OAAO,CAAiB;YAPzG,QAAG,GAAG,EAAE,EAAE,CAAC;YACX,WAAM,GAAG,KAAK,CAAC;YA0Bf,gBAAW,GAAG,UAAC,MAAM,EAAE,OAAuB;gBAC5C,OAAA,KAAI,CAAC,SAAS;sBACV,KAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,MAAM,EAAE,OAAO,EAAE,KAAI,CAAC,SAAS,EAAE,KAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;sBAC3F,KAAI,CAAC,QAAQ;aAAA,CAAC;SAvByF;QAE7G,4BAAI,GAAJ;YAAA,iBAgBC;YAfC,IAAM,EAAE,GAAGN,aAAQ,CAAC,EAAE,CAAC;YACvB,IAAM,OAAO,GAAG,IAAIO,mBAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,IAAI,IAAK,OAAAF,WAAM,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,GAAA,EAAE,EAAE,CAAC,CAAC;YAElF,IAAM,QAAQ,GAAQ;gBACpB,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;gBAC1E,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;aACjD,CAAC;YAEF,OAAO,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAC,OAAO;gBACnCG,UAAK,CAAC,qBAAqB,CAAC,QAAQ,EAAE,KAAI,CAAC,CAAC;gBAC5C,KAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;gBACrCH,WAAM,CAAC,KAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAC/B,OAAO,KAAI,CAAC;aACb,CAAC,CAAC;SACJ;;;;;;QAYD,qCAAa,GAAb,UAAc,OAAuB;YACnC,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC;YAClD,IAAI,CAACI,iBAAY,CAAC,QAAQ,CAAC;gBAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC7D,IAAM,IAAI,GAAGT,aAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACnD,IAAM,UAAU,GAAGU,YAAO,CAAC,QAAQ,CAAC,GAAGC,SAAI,CAAM,QAAQ,CAAC,GAAG,QAAQ,CAAC;YACtE,IAAM,UAAU,GAAG,IAAIC,eAAU,CAAC,EAAE,EAAO,UAAU,EAAE,IAAI,CAAC,CAAC;YAC7D,OAAO,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;SAChC;QACH,oBAAC;IAAD,CAAC;;ICxJD;IAoBA;;;IAGA;QAAA;YAAA,iBAuLC;2BAtLwB,aAAQ,GAAGC,EAAO,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;2BAK7C,SAAI,GAAG;gBACpB,OAAO;gBACP,gBAAgB;gBAChB,WAAW;gBACX,UAAC,KAAK,EAAE,cAAc,EAAE,SAAS;oBAC/B,KAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC,GAAG,IAAI,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;oBAChH,KAAI,CAAC,KAAK,GAAG,KAAK,CAAC;oBACnB,KAAI,CAAC,cAAc,GAAG,cAAc,CAAC;oBACrC,OAAO,KAAI,CAAC;iBACb;aACF,CAAC;SAuKH;;QApKC,wCAAc,GAAd,UAAe,KAAc;YAC3B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;SACvB;;;;;;;;;;;;;;QAeD,oCAAU,GAAV,UACE,MAA0B,EAC1B,MAAW,EACX,OAAuB;YAEvB,IAAM,eAAe,GAAG,qBAAqB,CAAC;YAE9C,IAAM,UAAU,GAAG,UAAC,MAAM,IAAK,OAAAb,aAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAC,GAAG,IAAK,QAAC,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAC,CAAC,GAAA,CAAC;YAC3F,IAAM,WAAW,GAAG,UAAC,MAAM,IAAK,OAAAA,aAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAC,GAAG,IAAK,QAAC,EAAE,SAAS,EAAE,GAAG,EAAE,IAAC,CAAC,GAAA,CAAC;YAE7F,OAAOC,cAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;kBAC7B,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;kBACpDA,cAAS,CAAC,MAAM,CAAC,WAAW,CAAC;sBAC7B,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;sBACpDA,cAAS,CAAC,MAAM,CAAC,gBAAgB,CAAC;0BAClC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,gBAAgB,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;0BACvEA,cAAS,CAAC,MAAM,CAAC,SAAS,CAAC;8BAC3B,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC;8BAC7BA,cAAS,CAAC,MAAM,CAAC,iBAAiB,CAAC;kCACnC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,iBAAiB,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;kCAClF,UAAU,CAAC,eAAe,CAAC,CAAC;SACjC;;;;;;;;;;QAWD,oCAAU,GAAV,UAAW,QAA2B,EAAE,MAAkB;YACxD,OAAOa,eAAU,CAAC,QAAQ,CAAC,GAAS,QAAS,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;SAClE;;;;;;;;;;QAWD,iCAAO,GAAP,UAAQ,GAAsB,EAAE,MAAW;YACzC,IAAIA,eAAU,CAAC,GAAG,CAAC;gBAAE,GAAG,GAAS,GAAI,CAAC,MAAM,CAAC,CAAC;YAC9C,IAAI,GAAG,IAAI,IAAI;gBAAE,OAAO,IAAI,CAAC;YAE7B,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,OAAO,IAAI,CAAC,KAAK;qBACd,GAAG,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,CAAC;qBAC1E,IAAI,CAAC,UAAU,QAAQ;oBACtB,OAAO,QAAQ,CAAC,IAAI,CAAC;iBACtB,CAAC,CAAC;aACN;YAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;SACnC;;;;;;;;;QAUD,sCAAY,GAAZ,UAAa,QAAqB,EAAE,MAAW,EAAE,OAAuB;YACtE,IAAM,IAAI,GAAGd,aAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACnD,IAAM,UAAU,GAAGU,YAAO,CAAC,QAAQ,CAAC,GAAGC,SAAI,CAAQ,QAAQ,CAAC,GAAG,QAAQ,CAAC;YACxE,IAAM,UAAU,GAAG,IAAIC,eAAU,CAAC,EAAE,EAAY,UAAU,EAAE,IAAI,CAAC,CAAC;YAClE,OAAO,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;SAChC;;;;;;;;QASD,+CAAqB,GAArB,UAAsB,QAAqB,EAAE,MAAW,EAAE,OAAuB;YAC/E,IAAM,IAAI,GAAGZ,aAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACnD,IAAM,UAAU,GAAGU,YAAO,CAAC,QAAQ,CAAC,GAAGC,SAAI,CAAQ,QAAQ,CAAC,GAAG,QAAQ,CAAC;YACxE,IAAM,UAAU,GAAG,IAAIC,eAAU,CAAC,EAAE,EAAY,UAAU,EAAE,IAAI,CAAC,CAAC;YAClE,OAAO,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;SAChC;;;;;;;;;;;;;;;QAgBD,+CAAqB,GAArB,UAAsB,MAAwB,EAAE,OAAuB,EAAE,SAAiB,EAAE,QAAc;YACxG,QAAQ,GAAG,QAAQ,IAAI,EAAE,CAAC;;YAG1B,IAAM,MAAM,GAAGC,EAAO,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;;YAEtD,IAAM,KAAK,GAAG,UAAC,SAAiB;gBAC9B,IAAM,OAAO,GAAGE,gBAAW,CAAC,SAAS,CAAC,CAAC;gBACvC,OAAO,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,OAAK,OAAS,GAAG,OAAO,CAAC;aAC9D,CAAC;YAEF,IAAM,YAAY,GAAG,UAAC,KAAmB;gBAC/B,IAAA,IAAI,GAAW,KAAK,KAAhB,EAAE,IAAI,GAAK,KAAK,KAAV,CAAW;gBAC7B,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;;;;gBAI7B,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;oBAAE,OAAU,QAAQ,UAAK,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAG,CAAC;gBAE9F,IAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;;;gBAG3C,IAAI,IAAI,KAAK,GAAG;oBAAE,OAAU,QAAQ,YAAO,MAAM,iBAAY,WAAW,QAAK,CAAC;;;;gBAK9E,IAAI,IAAI,KAAK,GAAG,EAAE;oBAChB,IAAM,GAAG,GAAG,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;oBAC/C,IAAM,EAAE,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC;oBAC3B,IAAM,IAAI,GAAG,CAAC,EAAE,IAAIf,aAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;;oBAE3D,IAAM,WAAW,GAAGU,YAAO,CAAC,EAAE,CAAC,GAAG,OAAI,EAAE,CAAC,MAAM,GAAG,CAAC,OAAG,GAAG,EAAE,CAAC;oBAC5D,OAAU,QAAQ,mBAAc,WAAW,GAAG,WAAW,SAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAI,CAAC;iBACjF;;gBAGD,OAAU,QAAQ,UAAK,MAAM,iBAAY,WAAW,MAAG,CAAC;aACzD,CAAC;YAEF,IAAM,KAAK,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC1E,IAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;YACnC,OAAO,MAAI,SAAS,SAAI,KAAK,WAAM,SAAS,MAAG,CAAC;SACjD;QACH,sBAAC;IAAD,CAAC,IAAA;IAED;IACA,SAAS,oBAAoB,CAAC,IAAY;QACxC,IAAM,OAAO,GAAUV,aAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC;QAClE,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;YAAE,MAAM,IAAI,KAAK,CAAC,qCAAmC,IAAI,MAAG,CAAC,CAAC;QAC7F,OAAO,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,MAAM,CAACgB,YAAO,EAAE,EAAE,CAAC,CAAC;IACtD,CAAC;IAED;IACA;IACA,IAAM,WAAW,GAAG,UAAC,GAAQ;QAC3B,IAAIC,aAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC;YAAE,OAAO,aAAa,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAC/E,OAAO,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC,CAAC;IAOF;IACA;IACA,IAAM,aAAa,GAAG,UAAC,WAAgB;QACrC,OAAA,MAAM,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC;;aAE3B,GAAG,CAAC,UAAC,GAAG,IAAK,OAAA,CAAC,GAAG,EAAE,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,GAAA,CAAC;;aAE/D,MAAM,CAAC,UAAC,KAAK,IAAK,OAAAhB,cAAS,CAAC,KAAK,CAAC,IAAIS,YAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAA,CAAC;;aAExD,GAAG,CAAC,UAAC,KAAK,IAAK,QAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAmB,IAAA,CAAC;IANzF,CAMyF;;IC5O3F;IAYA;;;;;;;;;;;;;;;;;QAiBE,uBAAoB,aAA4B,EAAU,YAA0B;YAAhE,kBAAa,GAAb,aAAa,CAAe;YAAU,iBAAY,GAAZ,YAAY,CAAc;YAClFQ,yBAAoB,CAACC,QAAG,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEA,QAAG,CAAC,IAAI,CAAC,CAAC,CAAC;SACrE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QA2FD,iCAAS,GAAT,UAAU,IAAY,EAAE,IAAqB;YAC3C,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC;SACzD;QAwID,6BAAK,GAAL,UAAM,IAAS,EAAE,UAAgB;YAC/B,IAAIF,aAAQ,CAAC,IAAI,CAAC,EAAE;gBAClB,UAAU,GAAG,IAAI,CAAC;aACnB;iBAAM;gBACL,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;aACxB;YACD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YACxC,OAAO,IAAI,CAAC;SACb;;;;;;QAQD,iCAAS,GAAT,UAAU,QAA2B;YACnC,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;SAC9C;QACH,oBAAC;IAAD,CAAC;;ICvRD;IAaA;;;;;;;;;IASO,IAAM,mBAAmB,GAAG,UAAC,QAA2C;QAC7E,OAAA,SAAS,gBAAgB,CAAC,WAAwB;YAChD,IAAM,IAAI,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;YACnC,IAAM,QAAQ,GAAG,QAAQ,KAAK,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;YAEvD,SAAS,gBAAgB,CAAC,KAAiB,EAAE,KAA0B;gBACrE,IAAM,cAAc,GAAG,IAAIV,mBAAc,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACvE,IAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC9D,IAAM,MAAM,GAAGF,WAAM,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,CAAC;gBACtF,OAAOL,aAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;aACtD;YAED,OAAO,IAAI,GAAG,gBAAgB,GAAG,SAAS,CAAC;SAC5C;IAZD,CAYC;;ICnCH;IAKA;;;;IAIA;QA6CE,6BAAY,iBAAoC;;YA3BxC,kBAAa,GAAe,EAAE,CAAC;YA4BrC,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;YAC3C,IAAM,GAAG,GAAGmB,QAAG,CAAC,iBAAiB,CAAC,CAAC;YACnCD,yBAAoB,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;SACtD;;;;;;;;;;;;;QAjBM,gDAA4B,GAAnC,UAAoC,MAAgB;YAClD,IAAM,QAAQ,GAAc,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAElE,QAAQ,CAAC,MAAM,GAAG,UAAC,CAAM;gBACvB,OAAA,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,UAAC,CAAC,IAAK,QAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAC,CAAC,GAAG,CAAC;aAAA,CAAC;YAEzF,QAAQ,CAAC,MAAM,GAAG,UAAC,CAAS;gBAC1B,OAAA,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,UAAC,CAAC,IAAK,QAAC,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,IAAC,CAAC,GAAG,CAAC;aAAA,CAAC;SAC5F;;QAGD,qCAAO,GAAP,eAAY;QAQZ,sCAAQ,GAAR,UAAS,QAAkB;YAA3B,iBAGC;YAFC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClC,OAAO,cAAM,OAAAE,eAAU,CAAC,KAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,GAAA,CAAC;SACvD;QAED,uCAAS,GAAT;YACE,IAAI,SAAS,GAAQ,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YACxD,SAAS,GAAGH,aAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC,OAAO,GAAG,SAAS,CAAC;YAChE,OAAO,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;SAC3C;QAED,sCAAQ,GAAR;YACE,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;SACxG;QAED,iCAAG,GAAH,UAAI,MAAe,EAAE,OAAe,EAAE,KAAM;YAAvB,wBAAA,EAAA,eAAe;YAClC,IAAIhB,cAAS,CAAC,MAAM,CAAC;gBAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAClD,IAAI,OAAO;gBAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACtC,IAAI,KAAK;gBAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACvC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;SAC7B;QAED,8CAAgB,GAAhB,UAAiB,UAAU,EAAE,SAA2B,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAuB;YAArG,iBAcC;YAbC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;YAC3B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;;YAGvB,UAAU,CAAC,GAAG,CAAC,wBAAwB,EAAE,UAAC,GAAG,IAAK,OAAA,KAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAC,EAAE,IAAK,OAAA,EAAE,CAAC,GAAG,CAAC,GAAA,CAAC,GAAA,CAAC,CAAC;YAC/F,IAAM,IAAI,GAAGkB,QAAG,CAAC,SAAS,CAAC,CAAC;;YAG5BD,yBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;;YAE9EA,yBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC;SACtE;QACH,0BAAC;IAAD,CAAC;;ICjGD;IAiBA;;;;;;;;;;;;;;;;;QAqBE,0CAAmC,MAAgB;YAAhB,WAAM,GAAN,MAAM,CAAU;SAAI;QALhD,mCAAiB,GAAxB,UAAyB,MAAgB,EAAE,OAAoB;YAC7D,OAAO,UAAC,KAAK,IAAK,OAAAlB,aAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,GAAA,CAAC;SACpH;;QAMD,gCAAI,GAAJ;YACE,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YAC1C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACnC,IAAI,CAAC,UAAU,CAAC,iBAAiB;gBAAE,UAAU,CAAC,MAAM,EAAE,CAAC;YACvD,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;SAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAiCD,gCAAI,GAAJ,UAAK,MAA0B;YAA/B,iBAQC;YAPC,IAAI,CAACc,eAAU,CAAC,MAAM,CAAC;gBAAE,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAEtE,IAAM,KAAK,GAAG,cAAM,OAAA,MAAM,CAACd,aAAQ,CAAC,SAAS,EAAE,KAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAA,CAAC;YAE5E,IAAM,IAAI,GAAG,IAAIqB,gBAAW,CAAC,KAAK,EAAEC,aAAQ,CAAC,CAAC;YAC9C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,OAAO,IAAI,CAAC;SACb;;;;;;;;;;;;;;;;;;;;;;;;;;;QA4BD,qCAAS,GAAT,UAAU,IAAiC;YAA3C,iBAWC;YAVC,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;YAC9C,IAAIlB,aAAQ,CAAC,IAAI,CAAC,EAAE;gBAClB,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;aAC1B;iBAAM,IAAIU,eAAU,CAAC,IAAI,CAAC,EAAE;gBAC3B,QAAQ,CAAC,SAAS,CAAC,cAAM,OAAA,IAAI,CAACd,aAAQ,CAAC,SAAS,EAAE,KAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAA,CAAC,CAAC;aACjF;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;aACxD;YAED,OAAO,IAAI,CAAC;SACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAwCD,gCAAI,GAAJ,UAAK,IAAkC,EAAE,OAA6B;YACpE,IAAIU,YAAO,CAAC,OAAO,CAAC,IAAII,eAAU,CAAC,OAAO,CAAC,EAAE;gBAC3C,OAAO,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;aACrE;YAED,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,OAAc,CAAC,CAAC;YACxD,OAAO,IAAI,CAAC;SACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAgCD,0CAAc,GAAd,UAAe,KAAe;YAC5B,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;SAC9C;QACH,wBAAC;IAAD,CAAC;;IChND;AA2CAD,MAAO,CAAC,MAAM,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;IACzC,IAAM,QAAQ,GAAGA,EAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1D,IAAM,QAAQ,GAAGA,EAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACtE,IAAM,OAAO,GAAGA,EAAO,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACvE,IAAM,SAAS,GAAGA,EAAO,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,oBAAoB,CAAC,CAAC,CAAC;IAClH,IAAM,QAAQ,GAAGA,EAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,oBAAoB,CAAC,CAAC,CAAC;IAC1G,IAAM,QAAQ,GAAGA,EAAO,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;IAWnE,IAAI,MAAM,GAAa,IAAI,CAAC;IAE5B,iBAAiB,CAAC,OAAO,GAAG,CAAC,mBAAmB,CAAC,CAAC;IAClD;IACA,SAAS,iBAAiB,CAAC,iBAAoC;;QAE7D,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,IAAIU,aAAQ,EAAE,CAAC;QACtC,MAAM,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,aAAa,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;;QAGpF,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;QACzD,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,EAAE,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;QACxE,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,UAAU,EAAE,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAC;QAC5E,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,SAAS,EAAE,mBAAmB,CAAC,SAAS,CAAC,CAAC,CAAC;QAE1E,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,kBAAkB,CAAC,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;;QAGnF,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC;QAE/C,IAAM,kBAAkB,IAAI,MAAM,CAAC,eAAe,GAAG,MAAM,CAAC,cAAc,GAAG,IAAI,mBAAmB,CAClG,iBAAiB,CAClB,CAAC,CAAC;QAEH,mBAAmB,CAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC;;QAGzD,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;QAC1B,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;QACzG,SAAS,IAAI,CACX,SAA2B,EAC3B,QAAa,EACb,OAAY,EACZ,QAAa,EACb,UAAqB,EACrB,KAAmB,EACnB,cAAqC;YAErC,kBAAkB,CAAC,gBAAgB,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;YACxF,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;YACxB,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC;YACtB,OAAO,MAAM,CAAC;SACf;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,IAAM,cAAc,GAAG,UAAC,WAAW,IAAK,OAAA;QACtC,mBAAmB;QACnB,UAAC,IAAI;YACH,IAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YACzC,OAAO,CAAC,MAAM,CAAC,GAAG,cAAM,OAAA,OAAO,GAAA,CAAC;YAChC,OAAO,OAAO,CAAC;SAChB;KACF,GAAA,CAAC;IAEF;IACA,QAAQ,CAAC,OAAO,GAAG,CAAC,WAAW,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;IACpD,SAAS,QAAQ,CAAC,SAA2B,EAAE,EAAa,EAAE,SAAmB;QAC/EvB,aAAQ,CAAC,SAAS,GAAG,SAAS,CAAC;QAC/BA,aAAQ,CAAC,EAAE,GAAQ,EAAE,CAAC;;QAGtB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE;YAChE,IAAI;gBACF,SAAS,CAAC,MAAM,CAAC,UAAU,aAAa,KAAI,CAAC,CAAC;aAC/C;YAAC,OAAO,KAAK,EAAE;gBACd,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;aACtE;SACF;;;QAID,SAAS,CAAC,aAAa;aACpB,GAAG,EAAE;aACL,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,OAAO,EAAE,CAAC,WAAW,GAAA,CAAC;aACnC,MAAM,CAACgB,YAAO,EAAE,EAAE,CAAC;aACnB,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,IAAI,KAAK,UAAU,GAAA,CAAC;aACpC,OAAO,CAAC,UAAC,UAAU,IAAK,QAAC,UAAU,CAAC,IAAI,GAAG,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,QAAQ,CAAC,IAAC,CAAC,CAAC;IAC/G,CAAC;IAED;IACA,IAAM,oBAAoB,GAAG,UAAC,QAAkB,IAAK,QAAC,QAAQ,CAAC,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,QAAQ,CAAC,IAAC,CAAC;IAEpH;IACA;IACA,IAAM,gBAAgB,GAAG,cAAM,OAAAX,WAAM,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE,cAAM,OAAA,MAAM,CAAC,YAAY,GAAA,EAAE,CAAC,GAAA,CAAC;IAEjG,YAAY,CAAC,OAAO,GAAG,CAAC,YAAY,CAAC,CAAC;aACtB,YAAY,CAAC,UAA6B;QACxD,UAAU,CAAC,MAAM,CAAC;YAChBG,UAAK,CAAC,kBAAkB,EAAE,CAAC;SAC5B,CAAC,CAAC;IACL,CAAC;IAED,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAO,iBAAiB,CAAC,CAAC;IACvD,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,CAAC,CAAC;IAC5E,QAAQ,CAAC,QAAQ,CAAC,aAAa,EAAE,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC;IAC/D,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC,mBAAmB,EAAE,cAAM,OAAA,MAAM,CAAC,iBAAiB,GAAA,CAAC,CAAC,CAAC;IAC/F,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,EAAE,cAAM,OAAA,IAAI,eAAe,EAAE,GAAA,CAAC,CAAC;IACnE,SAAS,CAAC,QAAQ,CAAC,gBAAgB,EAAE,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC;IACtE,SAAS,CAAC,QAAQ,CAAC,kBAAkB,EAAE,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC;IAClE,SAAS,CAAC,QAAQ,CAAC,cAAc,EAAE,cAAc,CAAC,mBAAmB,CAAC,CAAC,CAAC;IACxE,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAEtE,SAAS,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,WAAW,EAAE,UAAC,SAAmB,IAAK,OAAA,SAAS,CAAC,OAAO,CAAC,MAAM,GAAA,CAAC,CAAC,CAAC;IACpG,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,cAAM,OAAA,MAAM,CAAC,WAAW,GAAA,CAAC,CAAC;IACpD,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,cAAM,OAAAA,UAAK,GAAA,CAAC,CAAC;IAExC,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAC3B,QAAQ,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,UAAU,kBAAqC,KAAI,CAAC,CAAC,CAAC;IAC1F,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,MAAoB,KAAI,CAAC,CAAC,CAAC;IAC9D,OAAO,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,UAAU,UAAqB,KAAI,CAAC,CAAC,CAAC;IACjE,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAEvB;QACa,SAAS,GAAG,UAAC,GAAmB;QAC3C,IAAM,MAAM,GAAG,GAAG,CAAC,SAAS,EAAE,CAAC,MAAM,CAACJ,aAAQ,CAAC,CAAC;QAEhD,IAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,UAAC,GAAG;YAC5B,IAAM,UAAU,GAAG,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YAC1C,IAAM,UAAU,GAAG,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC;YACnD,OAAO,CAAC,GAAG,EAAE,UAAU,KAAK,QAAQ,GAAG,UAAU,CAAC,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;SAC9E,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,MAAM,CAACoB,eAAU,EAAE,EAAE,CAAC,CAAC;IACvC;;IC1LA;IAwCA;IACA,SAAS,aAAa,CAAC,GAAW;QAChC,IAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QAClD,IAAI,UAAU;YAAE,GAAG,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;QAEhD,IAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;QAC/E,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,qBAAqB,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;QACvF,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;IACpE,CAAC;IAED;IACA,SAAS,YAAY,CAAC,EAAoB;QACxC,IAAM,OAAO,GAAgB,EAAE,CAAC,MAAM,EAAuB,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QACvF,IAAM,IAAI,GAAeC,UAAK,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC;QACrD,OAAO,IAAI,GAAGd,SAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC;IAClD,CAAC;IAED;IACA,SAAS,YAAY,CAAC,MAAoB,EAAE,QAA0B,EAAE,GAAQ;QAC9E,IAAM,OAAO,GAAG,GAAG,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;QACnD,IAAM,WAAW,GAAGN,WAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;QACjF,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QAClE,OAAO,EAAE,OAAO,SAAA,EAAE,aAAa,EAAE,GAAG,CAAC,aAAa,EAAE,WAAW,aAAA,EAAE,IAAI,MAAA,EAAE,CAAC;IAC1E,CAAC;IASD;IACA,SAAS,WAAW,CAAC,EAAoB;;QAEvC,IAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,4BAA4B,CAAC;QAC/F,IAAM,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC;QAEzC,OAAO;YACL,IAAI,EAAE,MAAM,GAAG,QAAQ,GAAG,KAAK,GAAG,YAAY,GAAG,MAAM;YACvD,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,KAAK,GAAG;YAClD,SAAS,EAAE,CAAC,MAAM;SACnB,CAAC;IACJ,CAAC;IAED;IACA,SAAS,SAAS,CAChB,EAAoB,EACpB,MAAoB,EACpB,QAAyB,EACzB,IAAc,EACd,MAAiB;QAEjB,OAAO,UAAU,CAAyB;YACxC,IAAM,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,MAAM,EAChC,MAAM,GAAG,MAAM,EAAE,CAAC;YAEpB,IAAI,EAAE,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,MAAM,IAAI,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE;;gBAE1F,IAAM,YAAU,GAAG,QAAQ,CAAC;oBAC1B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;wBACxB,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,aAAa,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;qBACrE;iBACF,CAAC,CAAC;gBACH,CAAC,CAAC,cAAc,EAAE,CAAC;;gBAGnB,IAAI,2BAAyB,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;gBAEtE,CAAC,CAAC,cAAc,GAAG;oBACjB,IAAI,2BAAyB,EAAE,IAAI,CAAC;wBAAE,QAAQ,CAAC,MAAM,CAAC,YAAU,CAAC,CAAC;iBACnE,CAAC;aACH;SACF,CAAC;IACJ,CAAC;IAED;IACA,SAAS,WAAW,CAAC,EAAoB,EAAE,MAAoB;QAC7D,OAAO;YACL,QAAQ,EAAE,YAAY,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,QAAQ;YAC7C,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,MAAM;SACf,CAAC;IACJ,CAAC;IAED;IACA,SAAS,UAAU,CAAC,OAAyB,EAAE,KAAa,EAAE,MAAqB,EAAE,WAAgB;QACnG,IAAI,MAAM,CAAC;QAEX,IAAI,WAAW,EAAE;YACf,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;SAC7B;QAED,IAAI,CAACK,YAAO,CAAC,MAAM,CAAC,EAAE;YACpB,MAAM,GAAG,CAAC,OAAO,CAAC,CAAC;SACpB;QAED,IAAM,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,IAAI,GAAG,MAAM,CAAC;QACtC,KAAoB,UAAM,EAAN,iBAAM,EAAN,oBAAM,EAAN,IAAM,EAAE;YAAvB,IAAM,OAAK,eAAA;YACd,OAAO,CAAC,EAAE,CAAC,CAAC,OAAK,EAAE,MAAM,CAAC,CAAC;SAC5B;QAED,KAAK,CAAC,GAAG,CAAC,UAAU,EAAE;YACpB,IAAM,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,KAAK,GAAG,QAAQ,CAAC;YAC3C,KAAoB,UAAM,EAAN,iBAAM,EAAN,oBAAM,EAAN,IAAM,EAAE;gBAAvB,IAAM,OAAK,eAAA;gBACd,OAAO,CAAC,GAAG,CAAC,CAAC,OAAK,EAAE,MAAa,CAAC,CAAC;aACpC;SACF,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAqIA,IAAI,eAA8B,CAAC;IACnC,eAAe,GAAG;QAChB,WAAW;QACX,UAAU;QACV,SAAS,kBAAkB,CAAC,SAAmB,EAAE,QAAyB;YACxE,IAAM,MAAM,GAAG,SAAS,CAAC,YAAY,CAAC;YAEtC,OAAO;gBACL,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,CAAC,gBAAgB,EAAE,kBAAkB,CAAC;gBAC/C,IAAI,EAAE,UAAU,KAAa,EAAE,OAAyB,EAAE,KAAU,EAAE,YAAiB;oBACrF,IAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;oBAClC,IAAM,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;oBAClD,IAAI,YAAY,GAAa,IAAI,CAAC;oBAElC,IAAM,MAAM,GAAG,EAAS,CAAC;oBACzB,IAAM,MAAM,GAAG,cAAM,OAAA,YAAY,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,GAAA,CAAC;oBAE3D,IAAM,GAAG,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;oBACxC,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC;oBAC3B,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;oBAE3E,SAAS,MAAM;wBACb,IAAM,GAAG,GAAG,MAAM,EAAE,CAAC;wBACrB,IAAI,YAAY;4BAAE,YAAY,EAAE,CAAC;wBACjC,IAAI,MAAM;4BAAE,YAAY,GAAG,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC;wBACjF,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI;4BAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;qBACvD;oBAED,IAAI,GAAG,CAAC,SAAS,EAAE;wBACjB,KAAK,CAAC,MAAM,CACV,GAAG,CAAC,SAAS,EACb,UAAU,GAAG;4BACX,MAAM,CAAC,aAAa,GAAGL,WAAM,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;4BACvC,MAAM,EAAE,CAAC;yBACV,EACD,IAAI,CACL,CAAC;wBACF,MAAM,CAAC,aAAa,GAAGA,WAAM,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;qBAC/D;oBAED,MAAM,EAAE,CAAC;oBAET,KAAK,CAAC,GAAG,CAAC,UAAU,EAAO,SAAS,CAAC,aAAa,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;oBAC5E,KAAK,CAAC,GAAG,CAAC,UAAU,EAAO,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;oBAE9E,IAAI,CAAC,IAAI,CAAC,SAAS;wBAAE,OAAO;oBAC5B,IAAM,MAAM,GAAG,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;oBAClE,UAAU,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;iBACxD;aACF,CAAC;SACH;KACF,CAAC;IAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoFA,IAAI,gBAA+B,CAAC;IACpC,gBAAgB,GAAG;QACjB,WAAW;QACX,UAAU;QACV,SAAS,yBAAyB,CAAC,SAAmB,EAAE,QAAyB;YAC/E,IAAM,MAAM,GAAG,SAAS,CAAC,YAAY,CAAC;YAEtC,OAAO;gBACL,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,CAAC,gBAAgB,EAAE,kBAAkB,CAAC;gBAC/C,IAAI,EAAE,UAAU,KAAa,EAAE,OAAyB,EAAE,KAAU,EAAE,YAAiB;oBACrF,IAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;oBAClC,IAAM,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;oBAClD,IAAI,YAAY,GAAa,IAAI,CAAC;oBAClC,IAAI,MAAM,CAAC;oBAEX,IAAM,MAAM,GAAG,EAAS,CAAC;oBACzB,IAAM,MAAM,GAAG,cAAM,OAAA,YAAY,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,GAAA,CAAC;oBAE3D,IAAM,UAAU,GAAG,CAAC,SAAS,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;oBAC/D,IAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,IAAI,IAAK,QAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAGqB,SAAI,GAAG,GAAG,IAAC,EAAE,EAAE,CAAC,CAAC;oBAEtF,SAAS,MAAM;wBACb,IAAM,GAAG,GAAG,MAAM,EAAE,CAAC;wBACrB,IAAI,YAAY;4BAAE,YAAY,EAAE,CAAC;wBACjC,IAAI,MAAM;4BAAE,YAAY,GAAG,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC;wBACjF,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI;4BAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;qBACvD;oBAED,UAAU,CAAC,OAAO,CAAC,UAAC,KAAK;wBACvB,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC;wBAEhE,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAC,IAAI;4BACzB,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;4BACvB,aAAa,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CACjC,IAAI,EACJ,UAAC,MAAM;gCACL,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;gCACvB,MAAM,EAAE,CAAC;6BACV,EACD,IAAI,CACL,CAAC;yBACH,CAAC,CAAC;qBACJ,CAAC,CAAC;oBAEH,MAAM,EAAE,CAAC;oBAET,KAAK,CAAC,GAAG,CAAC,UAAU,EAAO,SAAS,CAAC,aAAa,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;oBAC5E,KAAK,CAAC,GAAG,CAAC,UAAU,EAAO,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;oBAE9E,IAAI,CAAC,IAAI,CAAC,SAAS;wBAAE,OAAO;oBAC5B,MAAM,GAAG,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;oBAC5D,UAAU,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;iBACxD;aACF,CAAC;SACH;KACF,CAAC;IAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4FA,IAAI,qBAAoC,CAAC;IACzC,qBAAqB,GAAG;QACtB,QAAQ;QACR,cAAc;QACd,cAAc;QACd,WAAW;QACX,SAAS,wBAAwB,CAC/B,MAAoB,EACpB,YAAiB,EACjB,YAAiC,EACjC,SAAmB;YAEnB,OAAO;gBACL,QAAQ,EAAE,GAAG;gBACb,UAAU,EAAE;oBACV,QAAQ;oBACR,UAAU;oBACV,QAAQ;oBACR,UAAU,MAAc,EAAE,QAA0B,EAAE,MAAW;wBAC/D,IAAI,MAAM,GAAgB,EAAE,CAAC;wBAC7B,IAAI,aAAqB,CAAC;wBAC1B,IAAI,YAAiB,CAAC;;;;wBAKtB,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC,cAAc,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;wBAEzE,IAAI;4BACF,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;yBAClD;wBAAC,OAAO,CAAC,EAAE;;;yBAGX;wBACD,YAAY,GAAG,YAAY,IAAI,YAAY,CAAC,MAAM,CAAC,YAAY,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;wBACtF,6BAA6B,CAAC,YAAY,CAAC,CAAC;;wBAG5C,IAAI,CAAC,cAAc,GAAG,UAAU,QAAgB,EAAE,SAAc;;;4BAG9D,IAAIT,aAAQ,CAAC,YAAY,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gCAC/C,OAAO;6BACR;4BACD,IAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;4BAC/D,MAAM,EAAE,CAAC;4BACT,OAAO,UAAU,CAAC;yBACnB,CAAC;wBAEF,SAAS,qBAAqB,CAAC,KAAK;4BAClC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAES,SAAI,CAAC,CAAC;yBAClC;wBACD,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,mBAAmB,EAAE,CAAC,CAAC;wBAC9C,IAAI,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE;4BAChC,qBAAqB,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;yBACrD;wBAED,SAAS,mBAAmB;4BAC1B,IAAM,+BAA+B,GAAG,SAAS,CAAC,aAAa,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;4BACrG,IAAM,yBAAyB,GAAG,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,EAAE,qBAAqB,CAAC,CAAC;4BACjG,IAAM,oCAAoC,GAAG,MAAM,CAAC,GAAG,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;4BACvF,OAAO,SAAS,OAAO;gCACrB,+BAA+B,EAAE,CAAC;gCAClC,yBAAyB,EAAE,CAAC;gCAC5B,oCAAoC,EAAE,CAAC;6BACxC,CAAC;yBACH;wBAED,SAAS,mBAAmB;4BAC1B,6BAA6B,CAAC,YAAY,CAAC,CAAC;yBAC7C;wBAED,SAAS,6BAA6B,CAAC,gBAAqB;4BAC1D,IAAIT,aAAQ,CAAC,gBAAgB,CAAC,EAAE;gCAC9B,MAAM,GAAG,EAAE,CAAC;gCACZd,YAAO,CAAC,gBAAgB,EAAE,UAAU,WAA6C,EAAE,WAAmB;;oCAEpG,IAAM,gBAAgB,GAAG,UAAU,WAAmB,EAAE,WAAmB;wCACzE,IAAM,GAAG,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;wCACvC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,WAAW,CAAC,CAAC;qCAC/D,CAAC;oCAEF,IAAIC,aAAQ,CAAC,WAAW,CAAC,EAAE;;wCAEzB,gBAAgB,CAAC,WAAqB,EAAE,WAAW,CAAC,CAAC;qCACtD;yCAAM,IAAIM,YAAO,CAAC,WAAW,CAAC,EAAE;;wCAE/BP,YAAO,CAAC,WAAW,EAAE,UAAU,WAAmB;4CAChD,gBAAgB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;yCAC5C,CAAC,CAAC;qCACJ;iCACF,CAAC,CAAC;6BACJ;yBACF;wBAED,SAAS,QAAQ,CAAC,SAAiB,EAAE,WAAgB,EAAE,WAAmB;4BACxE,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;4BAE5D,IAAM,SAAS,GAAG;gCAChB,KAAK,EAAE,KAAK,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE;gCACnC,MAAM,EAAE,WAAW;gCACnB,WAAW,EAAE,WAAW;6BACzB,CAAC;4BAEF,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;4BAEvB,OAAO,SAAS,WAAW;gCACzBiB,eAAU,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC;6BAC/B,CAAC;yBACH;;wBAGD,SAAS,MAAM;4BACb,IAAM,YAAY,GAAG,UAAC,GAAG,IAAK,OAAA,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAACE,aAAQ,CAAC,GAAA,CAAC;4BAC/D,IAAM,UAAU,GAAG,UAAC,SAAsB;gCACxC,OAAA,SAAS;qCACN,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,WAAW,GAAA,CAAC;qCACzB,GAAG,CAAC,YAAY,CAAC;qCACjB,MAAM,CAACN,YAAO,EAAE,EAAE,CAAC;6BAAA,CAAC;4BAEzB,IAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAACW,UAAK,EAAE,EAAE,CAAC,CAAC;4BAC5F,IAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,GAAA,CAAC,CAAC,CAAC;4BAC/F,IAAM,iBAAiB,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,GAAA,CAAC,CAAC,MAAM,CAAC;4BAC3F,IAAM,YAAY,GAAG,iBAAiB,GAAG,YAAY,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC;4BAE1E,IAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,MAAM,CAACA,UAAK,EAAE,EAAE,CAAC,CAAC;4BACvE,IAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC,UAAC,GAAG,IAAK,OAAA,CAACC,YAAO,CAAC,UAAU,EAAE,GAAG,CAAC,GAAA,CAAC,CAAC;4BAE5E,MAAM,CAAC,UAAU,CAAC;gCAChB,UAAU,CAAC,OAAO,CAAC,UAAC,SAAS,IAAK,OAAA,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAA,CAAC,CAAC;gCAChE,aAAa,CAAC,OAAO,CAAC,UAAC,SAAS,IAAK,OAAA,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,GAAA,CAAC,CAAC;6BACvE,CAAC,CAAC;yBACJ;wBAED,MAAM,EAAE,CAAC;qBACV;iBACF;aACF,CAAC;SACH;KACF,CAAC;AAgBFf,MAAO;SACJ,MAAM,CAAC,iBAAiB,CAAC;SACzB,SAAS,CAAC,QAAQ,EAAE,eAAe,CAAC;SACpC,SAAS,CAAC,cAAc,EAAE,qBAAqB,CAAC;SAChD,SAAS,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;SAClD,SAAS,CAAC,SAAS,EAAE,gBAAgB,CAAC;;IC3tBzC;IAKA;;;;;;;;;;IAUA,cAAc,CAAC,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpC,SAAS,cAAc,CAAC,MAAoB;QAC1C,IAAM,QAAQ,GAAQ,UAAU,KAAkB,EAAE,MAAW,EAAE,OAAoC;YACnG,OAAO,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;SAC1C,CAAC;QACF,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;;;;;;;IAUA,sBAAsB,CAAC,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC5C,SAAS,sBAAsB,CAAC,MAAoB;QAClD,IAAM,cAAc,GAAQ,UAAU,KAAkB,EAAE,MAAW,EAAE,OAAmC;YACxG,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;SAChD,CAAC;QACF,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC;QAChC,OAAO,cAAc,CAAC;IACxB,CAAC;AAEDA,MAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC,MAAM,CAAC,iBAAiB,EAAE,sBAAsB,CAAC;;IC3CrH;IA8CA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6HO,IAAI,MAAqB,CAAC;IACjC;IACA,MAAM,GAAG;QACP,OAAO;QACP,UAAU;QACV,eAAe;QACf,cAAc;QACd,IAAI;QACJ,SAAS,cAAc,CACrB,KAAkB,EAClB,QAAa,EACb,aAAkB,EAClB,YAAiC,EACjC,EAAU;YAEV,SAAS,WAAW;gBAClB,OAAO;oBACL,KAAK,EAAE,UAAU,OAAe,EAAE,MAAW,EAAE,EAAY;wBACzD,IAAIA,EAAO,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,EAAE;4BAC7B,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;yBAChD;6BAAM;4BACL,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;yBAC3C;qBACF;oBACD,KAAK,EAAE,UAAU,OAAe,EAAE,EAAY;wBAC5C,IAAIA,EAAO,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,EAAE;4BAC7B,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;yBAClC;6BAAM;4BACL,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;yBAC7B;qBACF;iBACF,CAAC;aACH;YAED,SAAS,YAAY,CAAC,OAAsB,EAAE,OAAsB;gBAClE,OAAO,OAAO,KAAK,OAAO,CAAC;aAC5B;YAED,IAAM,QAAQ,GAAG;gBACf,IAAI,EAAE,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,UAAU,CAAC,gBAAgB,EAAE,EAAE,EAAE;gBACrE,OAAO,EAAE,EAAE;aACZ,CAAC;YAEF,IAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,CAAC;gBACR,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,GAAG;gBACb,UAAU,EAAE,SAAS;gBACrB,OAAO,EAAE,UAAU,QAAgB,EAAE,MAAW,EAAE,WAAgC;oBAChF,OAAO,UAAU,KAAa,EAAE,QAA0B,EAAE,KAAU;wBACpE,IAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,EACrC,aAAa,GAAG,KAAK,CAAC,YAAY,CAAC,EACnC,QAAQ,GAAG,WAAW,EAAE,EACxB,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,QAAQ,EACzD,IAAI,GAAG,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC;wBAEnF,IAAI,UAAkB,EAAE,SAAiB,EAAE,YAAoB,EAAE,UAAyB,CAAC;wBAE3F,IAAM,YAAY,GAAiB;4BACjC,KAAK,EAAE,KAAK;4BACZ,EAAE,EAAE,SAAS,CAAC,KAAK,EAAE;4BACrB,IAAI,EAAE,IAAI;4BACV,GAAG,EAAE,SAAS,CAAC,OAAO,CAAC,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI;4BACtE,MAAM,EAAE,IAAI;4BACZ,aAAa,EAAE,qBAAqB;4BACpC,IAAI,eAAe;;gCAEjB,IAAM,mBAAmB,GAAGY,UAAK,CAAC,wBAAwB,CAAC,CAAC,SAAS,CAAC,CAAC;;;gCAGvE,IAAM,aAAa,GAAGA,UAAK,CAAC,yBAAyB,CAAC,CAAC,SAAS,CAAC,CAAC;gCAClE,OAAO,mBAAmB,IAAI,aAAa,CAAC;6BAC7C;yBACF,CAAC;wBAEFjB,UAAK,CAAC,gBAAgB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;wBAEhD,SAAS,qBAAqB,CAAC,MAAsB;4BACnD,IAAI,MAAM,IAAI,EAAE,MAAM,YAAY,aAAa,CAAC;gCAAE,OAAO;4BACzD,IAAI,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC;gCAAE,OAAO;4BAC7CA,UAAK,CAAC,wBAAwB,CAAC,YAAY,EAAE,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;4BAEpG,UAAU,GAAG,MAAM,CAAC;4BACpB,UAAU,CAAC,MAAM,CAAC,CAAC;yBACpB;wBAED,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC,CAAC;wBAEpD,UAAU,EAAE,CAAC;wBAEb,IAAM,UAAU,GAAG,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;wBACtD,KAAK,CAAC,GAAG,CAAC,UAAU,EAAE;4BACpBA,UAAK,CAAC,gBAAgB,CAAC,0BAA0B,EAAE,YAAY,CAAC,CAAC;4BACjE,UAAU,EAAE,CAAC;yBACd,CAAC,CAAC;wBAEH,SAAS,eAAe;4BACtB,IAAI,UAAU,EAAE;gCACdA,UAAK,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gCAC7E,UAAU,CAAC,MAAM,EAAE,CAAC;gCACpB,UAAU,GAAG,IAAI,CAAC;6BACnB;4BAED,IAAI,YAAY,EAAE;gCAChBA,UAAK,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;gCACzD,YAAY,CAAC,QAAQ,EAAE,CAAC;gCACxB,YAAY,GAAG,IAAI,CAAC;6BACrB;4BAED,IAAI,SAAS,EAAE;gCACb,IAAM,WAAS,GAAG,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gCAChDA,UAAK,CAAC,gBAAgB,CAAC,aAAa,EAAE,WAAS,CAAC,CAAC;gCACjD,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE;oCACxB,WAAS,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;oCAChC,UAAU,GAAG,IAAI,CAAC;iCACnB,CAAC,CAAC;gCAEH,UAAU,GAAG,SAAS,CAAC;gCACvB,SAAS,GAAG,IAAI,CAAC;6BAClB;yBACF;wBAED,SAAS,UAAU,CAAC,MAAsB;4BACxC,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;4BAC9B,IAAM,SAAS,GAAG,EAAE,CAAC,KAAK,EAAE,EAC1B,SAAS,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;4BAEzB,IAAM,WAAW,GAAe;gCAC9B,IAAI,EAAE,MAAM;gCACZ,OAAO,EAAE,YAAY;6BACtB,CAAC;4BAEF,IAAM,WAAW,GAAmB;gCAClC,UAAU,EAAE,SAAS,CAAC,OAAO;gCAC7B,UAAU,EAAE,SAAS,CAAC,OAAO;gCAC7B,WAAW,EAAE,SAAS;6BACvB,CAAC;;;;;;;;;;;;;4BAcF,QAAQ,CAAC,KAAK,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;4BAE5C,IAAM,MAAM,GAAG,WAAW,CAAC,QAAQ,EAAE,UAAU,KAAK;gCAClD,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;gCACvC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;gCACnC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,aAAa;oCACpD,SAAS,CAAC,OAAO,EAAE,CAAC;oCACpB,IAAI,YAAY;wCAAE,YAAY,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;oCAEnE,IAAI,CAACP,cAAS,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,KAAK,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE;wCAC9E,aAAa,CAAC,KAAK,CAAC,CAAC;qCACtB;iCACF,CAAC,CAAC;gCAEH,eAAe,EAAE,CAAC;6BACnB,CAAC,CAAC;4BAEH,SAAS,GAAG,MAAM,CAAC;4BACnB,YAAY,GAAG,QAAQ,CAAC;;;;;;;;;;;4BAWxB,YAAY,CAAC,KAAK,CAAC,oBAAoB,EAAE,MAAM,IAAI,UAAU,CAAC,CAAC;4BAC/D,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;yBAC/B;qBACF,CAAC;iBACH;aACF,CAAC;YAEF,OAAO,SAAS,CAAC;SAClB;KACF,CAAC;IAEF,kBAAkB,CAAC,OAAO,GAAG,CAAC,UAAU,EAAE,aAAa,EAAE,cAAc,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAExF;IACA,SAAS,kBAAkB,CACzB,QAAiC,EACjC,WAAuC,EACvC,YAA+B,EAC/B,KAAkB,EAClB,EAAqB;QAErB,IAAM,eAAe,GAAGwB,UAAK,CAAC,uBAAuB,CAAC,CAAC;QACvD,IAAM,YAAY,GAAGA,UAAK,CAAC,oBAAoB,CAAC,CAAC;QAEjD,OAAO;YACL,QAAQ,EAAE,KAAK;YACf,QAAQ,EAAE,CAAC,GAAG;YACd,OAAO,EAAE,UAAU,QAAgB;gBACjC,IAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAChC,QAAQ,CAAC,KAAK,EAAE,CAAC;gBAEjB,OAAO,UAAU,KAAa,EAAE,QAAgB;oBAC9C,IAAM,IAAI,GAAe,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAClD,IAAI,CAAC,IAAI,EAAE;wBACT,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;wBACvB,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAS,CAAC,CAAC,KAAK,CAAC,CAAC;wBAC5C,OAAO;qBACR;oBAED,IAAM,GAAG,GAAkB,IAAI,CAAC,IAAI,IAAS,EAAE,QAAQ,EAAE,EAAE,EAAE,WAAW,EAAEC,SAAI,EAAE,CAAC;oBACjF,IAAM,UAAU,GAAmB,GAAG,CAAC,IAAI,IAAI,IAAInB,mBAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBAC5E,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,OAAO,CAAC,CAAC;oBAChEC,UAAK,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;oBAErD,IAAM,IAAI,GAAG,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAS,CAAC,CAAC;oBAClD,IAAM,UAAU,GAAG,GAAG,CAAC,UAAwC,CAAC;oBAChE,IAAM,YAAY,GAAW,eAAe,CAAC,GAAG,CAAC,CAAC;oBAClD,IAAM,SAAS,GAAW,YAAY,CAAC,GAAG,CAAC,CAAC;oBAC5C,IAAM,MAAM,GAAG,UAAU,IAAI,SAAS,CAAC,UAAU,CAAC,CAAC;oBAEnD,KAAK,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC;oBAE1B,IAAI,UAAU,EAAE;wBACd,IAAM,kBAAkB,IACtB,WAAW,CAAC,UAAU,EAAEH,WAAM,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC,CACnF,CAAC;wBACF,IAAI,YAAY,EAAE;4BAChB,KAAK,CAAC,YAAY,CAAC,GAAG,kBAAkB,CAAC;4BACzC,KAAK,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC;yBACzC;;;;;wBAOD,QAAQ,CAAC,IAAI,CAAC,yBAAyB,EAAE,kBAAkB,CAAC,CAAC;wBAC7D,QAAQ,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,yBAAyB,EAAE,kBAAkB,CAAC,CAAC;wBAExE,2BAA2B,CAAC,EAAE,EAAE,YAAY,EAAE,kBAAkB,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;qBAC/E;;oBAGD,IAAID,aAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;wBAC3B,IAAM,SAAS,GAAGW,gBAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;wBAC7C,IAAM,WAAS,GAAG,IAAI,MAAM,CAAC,iBAAe,SAAS,MAAG,EAAE,GAAG,CAAC,CAAC;wBAE/D,IAAM,sBAAsB,GAAG;4BAC7B,IAAM,WAAW,GAAG,EAAE,CAAC,KAAK;iCACzB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;iCAC1B,MAAM,CAAC,UAAC,EAAW,IAAK,OAAA,EAAE,IAAI,EAAE,CAAC,OAAO,IAAI,WAAS,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,GAAA,CAAC,CAAC;4BAE3E,OAAO,WAAW,IAAIF,EAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,MAAI,GAAG,CAAC,SAAS,eAAY,CAAC,CAAC;yBACxF,CAAC;wBAEF,IAAM,iBAAe,GAAG,KAAK,CAAC,MAAM,CAAC,sBAAsB,EAAE,UAAU,YAAY;4BACjF,IAAI,CAAC,YAAY;gCAAE,OAAO;4BAC1B,2BAA2B,CAAC,EAAE,EAAE,YAAY,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;4BACxE,iBAAe,EAAE,CAAC;yBACnB,CAAC,CAAC;qBACJ;oBAED,IAAI,CAAC,KAAK,CAAC,CAAC;iBACb,CAAC;aACH;SACF,CAAC;IACJ,CAAC;IAED;IACA,IAAM,gBAAgB,GAAG,OAAQA,EAAe,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC,KAAK,UAAU,CAAC;IACjG;IACA,IAAI,YAAY,GAAG,CAAC,CAAC;IAErB;IACA,SAAS,2BAA2B,CAClC,EAAqB,EACrB,YAA+B,EAC/B,kBAAiC,EACjC,MAAc,EACd,GAAkB;;QAGlB,IACEC,eAAU,CAAC,kBAAkB,CAAC,OAAO,CAAC;YACtC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,IAAI,GAAG,CAAC,QAAQ,CAAC,iBAAiB,KAAK,gBAAgB,CAAC,EACjF;YACA,kBAAkB,CAAC,OAAO,EAAE,CAAC;SAC9B;QAED,IAAM,SAAS,GAAwBH,SAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;QAEjE,IAAM,WAAW,GAAmB,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC;;QAEjE,IAAIG,eAAU,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,EAAE;YACpD,IAAM,cAAc,GAAmB,IAAIP,mBAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACpE,IAAM,mBAAiB,GAAG,cAAc,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC;;YAG5E,IAAM,aAAa,GAAG,UAAC,YAAwB;;;gBAG7C,IAAI,YAAY,KAAK,mBAAiB,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,SAA6B,CAAC,KAAK,CAAC,CAAC;oBAC5G,OAAO;gBAET,IAAM,QAAQ,GAAG,YAAY,CAAC,MAAM,CAAC,IAAI,CAAkB,CAAC;gBAC5D,IAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAgB,MAAM,CAAkB,CAAC;gBAC/E,IAAM,aAAa,GAAG,UAAC,IAAc,IAAK,OAAA,IAAI,CAAC,WAAW,GAAA,CAAC;gBAC3D,IAAM,QAAQ,GAAY,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,MAAM,CAACS,YAAO,EAAE,EAAE,CAAC,CAAC;gBAChG,IAAM,UAAU,GAAY,YAAY,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,MAAM,CAACA,YAAO,EAAE,EAAE,CAAC,CAAC;;gBAGpG,IAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAC,KAAY;oBACnD,IAAM,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACtC,OAAO,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC7F,CAAC,CAAC;;gBAGH,IAAI,eAAe,CAAC,MAAM,EAAE;oBAC1B,IAAM,aAAW,GAAa,eAAe,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,EAAE,GAAA,CAAC,CAAC;;oBAE/D,IAAM,SAAS,GAAGa,WAAM,CAAC,QAAQ,EAAE,UAAC,GAAG,EAAE,GAAG,IAAK,OAAA,aAAW,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAA,CAAC,CAAC;oBAClF,kBAAkB,CAAC,iBAAiB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;iBAC/D;aACF,CAAC;YACF,MAAM,CAAC,GAAG,CAAC,UAAU,EAAO,YAAY,CAAC,SAAS,CAAC,EAAE,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC;SACrF;;QAGD,IAAIf,eAAU,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE;YAC5C,IAAM,IAAE,GAAG,YAAY,EAAE,CAAC;YAC1B,IAAM,WAAS,GAAG,eAAe,CAAC;;YAGlC,IAAM,kBAAgB,GAAG,UAAC,KAAiB;gBACzC,OAAA,CAAC,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,WAAS,CAAC,IAAI,KAAK,CAAC,WAAS,CAAC,CAAC,IAAE,CAAC,KAAK,IAAI,KAAK,kBAAgB,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC;aAAA,CAAC;;YAG/G,IAAM,WAAW,GAAG,UAAC,KAAiB;gBACpC,IAAI,OAAO,CAAC;gBACZ,IAAM,GAAG,IAAI,KAAK,CAAC,WAAS,CAAC,GAAG,KAAK,CAAC,WAAS,CAAC,IAAI,EAAE,CAAC,CAAC;gBAExD,IAAI,CAAC,kBAAgB,CAAC,KAAK,CAAC,EAAE;oBAC5B,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;oBACvD,OAAO,CAAC,IAAI,CAAC,UAAC,GAAG,IAAK,QAAC,GAAG,CAAC,IAAE,CAAC,GAAG,GAAG,KAAK,KAAK,IAAC,CAAC,CAAC;iBAClD;gBACD,OAAO,OAAO,CAAC;aAChB,CAAC;YAEF,IAAM,QAAQ,GAAG,EAAE,OAAO,EAAE,SAAS,CAAC,IAAI,EAAE,CAAC;YAC7C,MAAM,CAAC,GAAG,CAAC,UAAU,EAAO,YAAY,CAAC,QAAQ,CAAC,QAAQ,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC;SACxF;IACH,CAAC;AAEDD,MAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAO,MAAM,CAAC,CAAC;AACnEA,MAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAO,kBAAkB,CAAC;;ICvhB9E;IAgBA;IACA,SAAS,mBAAmB;QAC1B,IAAI,eAAe,GAAG,KAAK,CAAC;QAE5B,IAAI,CAAC,eAAe,GAAG;YACrB,eAAe,GAAG,IAAI,CAAC;SACxB,CAAC;QAEF,IAAI,CAAC,IAAI,GAAG;YACV,eAAe;YACf,UAAU;YACV,UAAU,aAAmC,EAAE,QAAyB;gBACtE,IAAI,eAAe,EAAE;oBACnB,OAAO,aAAa,CAAC;iBACtB;gBAED,OAAO,UAAU,QAAgB;oBAC/B,OAAO,QAAQ,CACb;wBACE,QAAQ,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC;qBAC9B,EACD,CAAC,EACD,KAAK,CACN,CAAC;iBACH,CAAC;aACH;SACF,CAAC;IACJ,CAAC;AAEDA,MAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,eAAe,EAA2B,mBAAmB,CAAC;;IC7CzG;;;;AAgBA,gBAAe,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}