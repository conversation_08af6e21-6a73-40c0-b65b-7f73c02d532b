{"version": 3, "file": "resolveService.min.js", "sources": ["angular-ui-router/src/legacy/resolveService.ts"], "sourcesContent": ["/** @publicapi @module ng1 */ /** */\nimport { StateObject, PathNode, ResolveContext, Obj, mapObj, resolvablesBuilder } from '@uirouter/core';\nimport * as angular from 'angular';\n\n/**\n * Implementation of the legacy `$resolve` service for angular 1.\n */\nconst $resolve = {\n  /**\n   * Asynchronously injects a resolve block.\n   *\n   * This emulates most of the behavior of the ui-router 0.2.x $resolve.resolve() service API.\n   *\n   * ### Not bundled by default\n   *\n   * This API is no longer not part of the standard `@uirouter/angularjs` bundle.\n   * For users of the prebuilt bundles, add the `release/resolveService.min.js` UMD bundle.\n   * For bundlers (webpack, browserify, etc), add `@uirouter/angularjs/lib/legacy/resolveService`.\n   *\n   * ---\n   *\n   * Given an object `invocables`, where keys are strings and values are injectable functions,\n   * injects each function, and waits for the resulting promise to resolve.\n   * When all resulting promises are resolved, returns the results as an object.\n   *\n   * #### Example:\n   * ```js\n   * let invocables = {\n   *   foo: [ '$http', ($http) =>\n   *            $http.get('/api/foo').then(resp => resp.data) ],\n   *   bar: [ 'foo', '$http', (foo, $http) =>\n   *            $http.get('/api/bar/' + foo.barId).then(resp => resp.data) ]\n   * }\n   * $resolve.resolve(invocables)\n   *     .then(results => console.log(results.foo, results.bar))\n   * // Logs foo and bar:\n   * // { id: 123, barId: 456, fooData: 'foo data' }\n   * // { id: 456, barData: 'bar data' }\n   * ```\n   *\n   * @param invocables an object which looks like an [[StateDeclaration.resolve]] object; keys are resolve names and values are injectable functions\n   * @param locals key/value pre-resolved data (locals)\n   * @param parent a promise for a \"parent resolve\"\n   */\n  resolve: (invocables: { [key: string]: Function }, locals = {}, parent?: Promise<any>) => {\n    const parentNode = new PathNode(new StateObject(<any>{ params: {}, resolvables: [] }));\n    const node = new PathNode(new StateObject(<any>{ params: {}, resolvables: [] }));\n    const context = new ResolveContext([parentNode, node]);\n\n    context.addResolvables(resolvablesBuilder(<any>{ resolve: invocables }), node.state);\n\n    const resolveData = (parentLocals: Obj) => {\n      const rewrap = (_locals: Obj) => resolvablesBuilder(<any>{ resolve: mapObj(_locals, (local) => () => local) });\n      context.addResolvables(rewrap(parentLocals), parentNode.state);\n      context.addResolvables(rewrap(locals), node.state);\n\n      const tuples2ObjR = (acc: Obj, tuple: { token: any; value: any }) => {\n        acc[tuple.token] = tuple.value;\n        return acc;\n      };\n      return context.resolvePath().then((results) => results.reduce(tuples2ObjR, {}));\n    };\n\n    return parent ? parent.then(resolveData) : resolveData({});\n  },\n};\n\n/** @hidden */\nexport const resolveFactory = () => $resolve;\n\n// The old $resolve service\nangular.module('ui.router').factory('$resolve', <any>resolveFactory);\n"], "names": ["$resolve", "resolve", "invocables", "locals", "parent", "parentNode", "PathNode", "StateObject", "params", "resolvables", "node", "context", "ResolveContext", "addResolvables", "resolvablesBuilder", "state", "resolveData", "parentLocals", "rewrap", "_locals", "mapObj", "local", "tuples2ObjR", "acc", "tuple", "token", "value", "<PERSON><PERSON><PERSON>", "then", "results", "reduce", "resolveFactory", "factory"], "mappings": ";;;;;;8UAOA,IAAMA,EAAW,CAqCfC,QAAS,SAACC,EAAyCC,EAAaC,gBAAbD,MACjD,IAAME,EAAa,IAAIC,WAAS,IAAIC,cAAiB,CAAEC,OAAQ,GAAIC,YAAa,MAC1EC,EAAO,IAAIJ,WAAS,IAAIC,cAAiB,CAAEC,OAAQ,GAAIC,YAAa,MACpEE,EAAU,IAAIC,iBAAe,CAACP,EAAYK,IAEhDC,EAAQE,eAAeC,qBAAwB,CAAEb,QAASC,IAAeQ,EAAKK,OAE9E,IAAMC,EAAc,SAACC,GACnB,IAAMC,EAAS,SAACC,GAAiB,OAAAL,qBAAwB,CAAEb,QAASmB,SAAOD,GAAS,SAACE,GAAU,OAAA,WAAM,OAAAA,SACrGV,EAAQE,eAAeK,EAAOD,GAAeZ,EAAWU,OACxDJ,EAAQE,eAAeK,EAAOf,GAASO,EAAKK,OAE5C,IAAMO,EAAc,SAACC,EAAUC,GAE7B,OADAD,EAAIC,EAAMC,OAASD,EAAME,MAClBH,GAET,OAAOZ,EAAQgB,cAAcC,MAAK,SAACC,GAAY,OAAAA,EAAQC,OAAOR,EAAa,QAG7E,OAAOlB,EAASA,EAAOwB,KAAKZ,GAAeA,EAAY,MAK9Ce,EAAiB,WAAM,OAAA/B,YAGrB,aAAagC,QAAQ,WAAiBD"}