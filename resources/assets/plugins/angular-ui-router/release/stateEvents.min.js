/**
 * State-based routing for AngularJS 1.x
 * @version v1.0.30
 * @link https://ui-router.github.io
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("angular")):"function"==typeof define&&define.amd?define(["exports","angular"],e):e((t=t||self)["@uirouter/angularjs-state-events"]={},t.angular)}(this,(function(t,e){"use strict";var r=angular,n=e&&e.module?e:r;!function(){var t=n.isFunction,e=n.isString;function r(t,r){var n,o;if(Array.isArray(r)&&(n=r[0],o=r[1]),!e(n))throw new Error("invalid parameters to applyPairs");return t[n]=o,t}function o(t){if(t.options().notify&&t.valid()&&!t.ignored()){var e=t.injector(),r=e.get("$stateEvents"),n=e.get("$rootScope"),o=e.get("$state"),a=e.get("$urlRouter"),s=r.provider.enabled(),i=t.params("to"),u=t.params("from");if(s.$stateChangeSuccess){if(n.$broadcast("$stateChangeStart",t.to(),i,t.from(),u,t.options(),t).defaultPrevented)return s.$stateChangeCancel&&n.$broadcast("$stateChangeCancel",t.to(),i,t.from(),u,t.options(),t),null==o.transition&&a.update(),!1;t.onSuccess({},(function(){n.$broadcast("$stateChangeSuccess",t.to(),i,t.from(),u,t.options(),t)}),{priority:9999})}s.$stateChangeError&&t.promise.catch((function(e){(!e||2!==e.type&&3!==e.type)&&(n.$broadcast("$stateChangeError",t.to(),i,t.from(),u,e,t.options(),t).defaultPrevented||a.update())}))}}function a(e,r,n){var o=n.get("$state"),a=n.get("$rootScope"),s=n.get("$urlRouter"),i={to:e.identifier(),toParams:e.params(),options:e.options()},u=a.$broadcast("$stateNotFound",i,r.state(),r.params());function c(){return o.target(i.to,i.toParams,i.options)}return(u.defaultPrevented||u.retry)&&s.update(),!u.defaultPrevented&&(u.retry||o.get(i.to)?u.retry&&t(u.retry.then)?u.retry.then(c):c():void 0)}function s(t){s.prototype.instance=this;var e=!1,n=["$stateChangeStart","$stateNotFound","$stateChangeSuccess","$stateChangeError"],i=n.map((function(t){return[t,!0]})).reduce(r,{});function u(){if(e)throw new Error("Cannot enable events at runtime (use $stateEventsProvider")}function c(r){return e=!0,i.$stateNotFound&&t.onInvalid(a),i.$stateChangeStart&&r.onBefore({},o,{priority:1e3}),{provider:s.prototype.instance}}this.enable=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];u(),t&&t.length||(t=n),t.forEach((function(t){return i[t]=!0}))},this.disable=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];u(),t&&t.length||(t=n),t.forEach((function(t){return delete i[t]}))},this.enabled=function(){return i},this.$get=c,c.$inject=["$transitions"]}a.$inject=["$to$","$from$","$state","$rootScope","$urlRouter"],s.$inject=["$stateProvider"],n.module("ui.router.state.events",["ui.router.state"]).provider("$stateEvents",s).run(["$stateEvents",function(t){}])}(),t.$stateChangeCancel=void 0,t.$stateChangeError=void 0,t.$stateChangeStart=void 0,t.$stateChangeSuccess=void 0,t.$stateNotFound=void 0,Object.defineProperty(t,"__esModule",{value:!0})}));
//# sourceMappingURL=stateEvents.min.js.map
