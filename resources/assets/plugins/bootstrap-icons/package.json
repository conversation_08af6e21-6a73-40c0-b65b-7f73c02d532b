{"name": "bootstrap-icons", "version": "1.8.1", "description": "Official open source SVG icon library for Bootstrap", "author": "mdo", "license": "MIT", "homepage": "https://icons.getbootstrap.com/", "repository": {"type": "git", "url": "git+https://github.com/twbs/icons.git"}, "bugs": {"url": "https://github.com/twbs/icons/issues"}, "keywords": ["bootstrap", "icons"], "scripts": {"start": "npm run docs-serve", "docs-serve": "hugo server --port 4000 --disableFastRender", "docs-build": "hugo --cleanDestinationDir", "docs-purge": "npm run docs-build && purgecss --css docs/static/assets/css/bootstrap.min.css --content \"_site/**/*.html\" \"_site/assets/js/**/*.js\" --keyframes --output docs/static/assets/css/", "pages": "node build/build-pages.js", "icons": "npm-run-all icons-main --aggregate-output --parallel icons-sprite icons-font", "icons-main": "node build/build-svgs.js", "icons-zip": "cross-env-shell \"rm -rf bootstrap-icons-$npm_package_version && cp -r icons/ bootstrap-icons-$npm_package_version && cp bootstrap-icons.svg bootstrap-icons-$npm_package_version && cp -r font/ bootstrap-icons-$npm_package_version && zip -r9 bootstrap-icons-$npm_package_version.zip bootstrap-icons-$npm_package_version && rm -rf bootstrap-icons-$npm_package_version\"", "icons-sprite": "svg-sprite --config svg-sprite.json --log=info icons/*.svg", "icons-font": "<PERSON>on", "release": "npm-run-all icons docs-build icons-zip", "netlify": "cross-env-shell HUGO_BASEURL=$DEPLOY_PRIME_URL npm-run-all icons docs-purge docs-build", "test:fusv": "fusv docs/assets/scss/", "test:eslint": "eslint --cache --cache-location node_modules/.cache/.eslintcache --report-unused-disable-directives .", "test:stylelint": "stylelint docs/assets/scss/ --cache --cache-location node_modules/.cache/.stylelintcache --rd", "test:linkinator": "linkinator _site --recurse --silent --skip \"^(?!http://localhost)\"", "test:lockfile-lint": "lockfile-lint --allowed-hosts npm --allowed-schemes https: --empty-hostname false --type npm --path package-lock.json", "test:vnu": "node build/vnu-jar.js", "test": "npm-run-all docs-build --parallel --aggregate-output --continue-on-error test:*"}, "style": "font/bootstrap-icons.css", "sass": "font/bootstrap-icons.scss", "devDependencies": {"autoprefixer": "^10.4.2", "cheerio": "^1.0.0-rc.10", "cross-env": "^7.0.3", "eslint": "^8.8.0", "fantasticon": "^1.2.3", "find-unused-sass-variables": "^4.0.1", "hugo-bin": "^0.80.1", "linkinator": "^3.0.3", "lockfile-lint": "^4.6.2", "npm-run-all": "^4.1.5", "picocolors": "^1.0.0", "postcss": "^8.4.5", "postcss-cli": "^9.1.0", "purgecss": "^4.1.3", "stylelint": "^14.2.0", "stylelint-config-twbs-bootstrap": "^3.0.1", "svg-sprite": "^1.5.3", "svgo": "^2.8.0", "vnu-jar": "21.10.12"}, "engines": {"node": ">=10"}, "files": ["icons/*.svg", "bootstrap-icons.svg", "font", "!.DS_Store"], "hugo-bin": {"buildTags": "extended"}}