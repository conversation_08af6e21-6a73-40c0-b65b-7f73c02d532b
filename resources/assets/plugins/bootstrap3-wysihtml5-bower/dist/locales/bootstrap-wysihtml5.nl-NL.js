/**
 * Dutch translation for bootstrap-wysihtml5
 */
(function (factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define('bootstrap.wysihtml5.nl-NL', ['jquery', 'bootstrap.wysihtml5'], factory);
    } else {
        // Browser globals
        factory(jQuery);
    }
}(function($){
    $.fn.wysihtml5.locale["nl-NL"] = {
        font_styles: {
            normal: "Normale Tekst",
            h1: "Kop 1",
            h2: "Kop 2",
            h3: "Kop 3"
        },
        emphasis: {
            bold: "Vet",
            italic: "Cursief",
            underline: "Onderstrepen"
        },
        lists: {
            unordered: "Ongeordende lijst",
            ordered: "Geordende lijst",
            outdent: "Inspringen verkleinen",
            indent: "Inspringen vergroten"
        },
        link: {
            insert: "Link invoegen",
            cancel: "Annule<PERSON>"
        },
        image: {
            insert: "Afbeelding invoegen",
            cancel: "Annuleren"
        },
        html: {
            edit: "HTML bewerken"
        },
        colours: {
            black: "<PERSON><PERSON>",
            silver: "<PERSON>il<PERSON>",
            gray: "Grij<PERSON>",
            maroon: "Ka<PERSON>jebruin",
            red: "Rood",
            purple: "Paars",
            green: "Groen",
            olive: "Olijfgroen",
            navy: "Donkerblauw",
            blue: "Blauw",
            orange: "Oranje"
        }
    };
}));
