<?php
#region region DOCS
/** @var Gaming[] $gamings */
/** @var Gaming[] $gamings_mmo */
/** @var Gaming[] $gamings_por_instalar */
/** @var array $platforms */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title>My Dash | Gaming</title>

	<!-- #head -->
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
	<style>
        .standby-indicator-border {
            /* Define un borde izquierdo usando el color warning de Bootstrap */
            border-left: 4px solid var(--bs-warning); /* Puedes ajustar el grosor (4px) */
        }
	</style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<h4>Juegos</h4>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<form action="agaming" method="POST">
			<input type="hidden" id="selected_game" name="selected_game">
			<input type="hidden" id="sel_id_game" name="sel_id_game">

			<?php #region region SUBMIT sub_editar_game ?>
			<div class="col" style="display: none">
				<button type="submit" id="sub_editar_game" name="sub_editar_game" class="btn btn-success w-100">
					sub_editar_game
				</button>
			</div>
			<?php #endregion sub_editar_game ?>

			<!-- BEGIN row for filter -->
			<div class="row mt-3">
				<div class="col-md-12">
					<input type="text" id="filterInput" class="form-control" placeholder="Filter games by name (min 3 chars)...">
				</div>
			</div>
			<!-- END row for filter -->

			<!-- BEGIN row -->
			<div class="row mt-3">
				<?php #region region LINK crear ?>
				<div class="col-md-12 col-xs-12">
					<a href="igaming" class="btn btn-md btn-primary w-100">
						Crear
					</a>
				</div>
				<?php #endregion LINK crear ?>
			</div>
			<!-- END row -->

			<!-- BEGIN row -->
			<div class="row">
				<?php #region region games single ?>
				<div class="col-md-7 col-xs-12">
					<?php #region region PANEL games single ?>
					<div class="panel panel-inverse mt-3">
						<div class="panel-heading">
							<h4 class="panel-title">Juegos</h4>
							<span class="badge bg-primary rounded-0 fs-12px">
                                <?php echo count($gamings); ?>
                            </span>
						</div>
						<!-- BEGIN panel-body -->
						<div class="panel-body p-0">

							<?php #region region TABLE games single ?>
							<table class="table table-hover table-sm">
								<thead>
								<tr>
									<th class="text-start">Plataforma</th>
									<th class="text-center w-10px"></th>
									<th class="text-center w-10px"></th>
									<th class="text-start">Juego</th>
								</tr>
								</thead>
								<tbody class="fs-12px" id="gamesTableBody">
								<?php foreach ($gamings as $gaming): ?>
									<?php
										// Determinar la clase condicional para el borde
										$standby_border_class = ($gaming->en_standby == 1) ? 'standby-indicator-border' : '';
									?>

									<tr class="cursor-pointer" onclick="editar_game('<?php echo limpiar_datos($gaming->id_gaming); ?>');">
										<td class="w-200px <?php echo $standby_border_class; ?>">
											<?php echo $gaming->platform->name; ?>
										</td>
										<td class="text-center">
											<?php if ($gaming->usa_borderlessapp == 1): ?>
												<i class="fa fa-desktop text-success fa-md fa-fw" title="Usa Borderless App"></i>
											<?php endif; ?>
										</td>
										<td class="text-center">
											<?php if ($gaming->input == 'kbm'): ?>
												<i class="fa fa-keyboard text-primary fa-fw" title="Teclado + Mouse"></i>
											<?php elseif ($gaming->input == 'controller'): ?>
												<i class="fa fa-gamepad text-primary fa-fw" title="Control"></i>
											<?php endif; ?>
										</td>
										<td>
											<?php echo $gaming->name; ?>
											<?php // Icono standby con tooltip actualizado ?>
											<?php if ($gaming->en_standby == 1): ?>
												<i class="fa fa-power-off text-warning fa-fw ms-1"
												   title="<?php echo htmlspecialchars($gaming->texto_standby ?? 'Standby (sin texto)', ENT_QUOTES); ?>">
											<?php endif; ?>
										</td>
									</tr>
								<?php endforeach; ?>
								</tbody>
							</table>
							<?php #endregion TABLE games single ?>
						</div>
						<!-- END panel-body -->
					</div>
					<?php #endregion PANEL games single ?>
				</div>
				<?php #endregion games single ?>
				<div class="col-md-5 col-xs-12">
					<?php #region region PANEL games por instalar ?>
					<div class="panel panel-inverse mt-3">
						<div class="panel-heading d-flex justify-content-between align-items-center">
							<div class="d-flex align-items-center">
								<h4 class="panel-title me-2">Por instalar</h4>
								<span class="badge bg-primary rounded-0 fs-12px">
	                                <?php echo count($gamings_por_instalar); ?>
	                            </span>
							</div>
							<button type="button" class="btn btn-sm btn-warning" id="btnEscogerAlAzar" title="Escoger al azar un juego listo para instalar">
								<i class="fa fa-random me-1"></i> Escoger al azar
							</button>
						</div>
						<!-- BEGIN panel-body -->
						<div class="panel-body p-0">
							<?php #region region TABLE games por instalar ?>
							<table class="table table-hover table-sm">
								<thead>
								<tr>
									<th class="text-start">Plataforma</th>
									<th class="text-center w-10px"></th>
									<th class="text-start" style="width: 700px">Juego</th>
								</tr>
								</thead>
								<tbody class="fs-12px" id="pendingGamesTableBody">
								<?php foreach ($gamings_por_instalar as $gaming_por_instalar): ?>
									<?php
									// Determinar la clase condicional para el borde
									$tooltip_text = trim($gaming_por_instalar->texto_por_instalar ?? '');
									?>

									<tr class="cursor-pointer"
									    onclick="editar_game('<?php echo limpiar_datos($gaming_por_instalar->id_gaming); ?>');"
									    title="<?php echo htmlspecialchars($tooltip_text, ENT_QUOTES); ?>"
									>
										<td class="w-200px">
											<?php echo $gaming_por_instalar->platform->name; ?>
										</td>
										<td class="text-center">
											<?php if ($gaming_por_instalar->listo_para_instalar == 1): ?>
												<i class="fa fa-check-square text-success fa-fw ms-1" title="Listo para instalar"></i>
													<?php else: ?>
														<i class="fa fa-times-circle text-danger fa-fw ms-1" title="<?php echo htmlspecialchars($tooltip_text !== '' ? $tooltip_text : 'No listo para instalar', ENT_QUOTES); ?>"></i>
											<?php endif; ?>
										</td>
										<td>
											<?php echo $gaming_por_instalar->name; ?>
										</td>
									</tr>
								<?php endforeach; ?>
								</tbody>
							</table>
							<?php #endregion TABLE games single ?>
						</div>
						<!-- END panel-body -->
					</div>
					<?php #endregion PANEL games single ?>
				</div>
			</div>
			<!-- END row -->
			<?php #region region MODAL mdl_revisar ?>
			<div class="modal fade" id="mdl_revisar">
				<div class="modal-dialog modal-lg modal-dialog-centered">
					<div class="modal-content">
						<input type="hidden" id="mdl_revisar_id_gaming" name="mdl_revisar_id_gaming">

						<div class="modal-header">
							<h4 class="modal-title">Revisar</h4>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
						</div>
						<div class="modal-body">
							<!-- BEGIN row -->
							<div class="row mt-3">
								<?php #region region SUBMIT sub_played ?>
								<div class="col-md-12 col-xs-12">
									<button type="submit" id="sub_played" name="sub_played" class="btn btn-lg btn-success w-100 no-border-radious">
										Jugado
									</button>
								</div>
								<?php #endregion SUBMIT sub_played ?>
							</div>
							<!-- END row -->
							<!-- BEGIN row -->
							<div class="row mt-3">
								<?php #region region SUBMIT sub_edit ?>
								<div class="col-md-12 col-xs-12">
									<button type="submit" id="sub_edit" name="sub_edit" class="btn btn-lg btn-primary w-100 no-border-radious">
										Editar
									</button>
								</div>
								<?php #endregion SUBMIT sub_edit ?>
							</div>
							<!-- END row -->
							<!-- BEGIN row -->
							<div class="row mt-3">
								<?php #region region SUBMIT sub_uninstall ?>
								<div class="col-md-12 col-xs-12">
									<button type="submit" id="sub_uninstall" name="sub_uninstall" class="btn btn-lg btn-danger w-100 no-border-radious">
										Desinstalar
									</button>
								</div>
								<?php #endregion SUBMIT sub_uninstall ?>
							</div>
							<!-- END row -->
						</div>
					</div>
				</div>
			</div>
			<?php #endregion MODAL mdl_revisar ?>

			<?php #region region MODAL mdl_random_gaming ?>
			<div class="modal fade" id="mdl_random_gaming">
				<div class="modal-dialog modal-lg modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header bg-warning text-dark">
							<h4 class="modal-title">
								<i class="fa fa-trophy me-2"></i>¡Juego Seleccionado!
							</h4>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
						</div>
						<div class="modal-body text-center">
							<div class="mb-4">
								<i class="fa fa-gamepad fa-4x text-warning mb-3"></i>
							</div>
							<h3 class="text-primary mb-3" id="randomGameName">-</h3>
							<div class="row">
								<div class="col-md-6">
									<div class="card bg-dark border-secondary mb-3">
										<div class="card-body">
											<h6 class="card-title text-warning">
												<i class="fa fa-desktop me-1"></i>Plataforma
											</h6>
											<p class="card-text" id="randomGamePlatform">-</p>
										</div>
									</div>
								</div>
								<div class="col-md-6">
									<div class="card bg-dark border-secondary mb-3">
										<div class="card-body">
											<h6 class="card-title text-warning">
												<i class="fa fa-keyboard me-1"></i>Control
											</h6>
											<p class="card-text" id="randomGameInput">-</p>
										</div>
									</div>
								</div>
							</div>
							<div class="card bg-dark border-secondary" id="randomGameNotesCard" style="display: none;">
								<div class="card-body">
									<h6 class="card-title text-warning">
										<i class="fa fa-sticky-note me-1"></i>Notas
									</h6>
									<p class="card-text text-start" id="randomGameNotes">-</p>
								</div>
							</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
							<button type="button" class="btn btn-primary" id="btnEditRandomGame">
								<i class="fa fa-edit me-1"></i>Editar Juego
							</button>
						</div>
					</div>
				</div>
			</div>
			<?php #endregion MODAL mdl_random_gaming ?>
		</form>
	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="javascript:;" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>

<?php #region region JS editar_game ?>
<script type="text/javascript">
    function editar_game($id_gaming) {
        const sel_id_game = document.getElementById('sel_id_game');
        sel_id_game.value = $id_gaming;

        document.getElementById('sub_editar_game').click();
    }
</script>
<?php #endregion JS editar_game ?>

<?php #region region JS filter ?>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const filterInput = document.getElementById('filterInput');
        const gamesTableBody = document.getElementById('gamesTableBody');
        const pendingGamesTableBody = document.getElementById('pendingGamesTableBody');
        const allGameRows = Array.from(gamesTableBody.querySelectorAll('tr'));
        const allPendingGameRows = Array.from(pendingGamesTableBody.querySelectorAll('tr'));

        filterInput.addEventListener('input', function () {
            const filterText = filterInput.value.toLowerCase().trim();

            const applyFilter = (rows) => {
                rows.forEach(row => {
                    // Assuming game name is in the fourth td (index 3) for games table, third td (index 2) for pending games table
                    const gameNameCell = row.cells[rows === allGameRows ? 3 : 2];
                    if (gameNameCell) {
                        const gameName = gameNameCell.textContent.toLowerCase().trim();
                        if (filterText.length >= 3) {
                            if (gameName.includes(filterText)) {
                                row.style.display = ''; // Show row
                            } else {
                                row.style.display = 'none'; // Hide row
                            }
                        } else {
                            row.style.display = ''; // Show all rows if filter is too short
                        }
                    }
                });
            };

            applyFilter(allGameRows);
            applyFilter(allPendingGameRows);
        });
    });
</script>
<?php #endregion JS filter ?>

<?php #region region JS random gaming ?>
<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function () {
        const btnEscogerAlAzar = document.getElementById('btnEscogerAlAzar');
        const modalRandomGaming = document.getElementById('mdl_random_gaming');
        const btnEditRandomGame = document.getElementById('btnEditRandomGame');

        let selectedRandomGameId = null;

        if (btnEscogerAlAzar) {
            btnEscogerAlAzar.addEventListener('click', function() {
                // Disable button and show loading state
                btnEscogerAlAzar.disabled = true;
                btnEscogerAlAzar.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i> Buscando...';

                // Make AJAX request
                fetch('src/fetch_random_gaming.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Populate modal with game data
                        document.getElementById('randomGameName').textContent = data.data.name;
                        document.getElementById('randomGamePlatform').textContent = data.data.platform_name;

                        // Format input method
                        const inputText = data.data.input === 'kbm' ? 'Teclado + Mouse' : 'Control';
                        document.getElementById('randomGameInput').textContent = inputText;

                        // Handle notes
                        const notesCard = document.getElementById('randomGameNotesCard');
                        const notesText = document.getElementById('randomGameNotes');
                        if (data.data.texto_por_instalar && data.data.texto_por_instalar.trim() !== '') {
                            notesText.textContent = data.data.texto_por_instalar;
                            notesCard.style.display = 'block';
                        } else {
                            notesCard.style.display = 'none';
                        }

                        // Store the game ID for editing
                        selectedRandomGameId = data.data.id_gaming;

                        // Show the modal
                        const modal = new bootstrap.Modal(modalRandomGaming);
                        modal.show();

                    } else {
                        // Show error message
                        alert(data.message || 'No se pudo obtener un juego aleatorio.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error al conectar con el servidor.');
                })
                .finally(() => {
                    // Re-enable button
                    btnEscogerAlAzar.disabled = false;
                    btnEscogerAlAzar.innerHTML = '<i class="fa fa-random me-1"></i> Escoger al azar';
                });
            });
        }

        // Handle edit button click
        if (btnEditRandomGame) {
            btnEditRandomGame.addEventListener('click', function() {
                if (selectedRandomGameId) {
                    // Close the modal first
                    const modal = bootstrap.Modal.getInstance(modalRandomGaming);
                    if (modal) {
                        modal.hide();
                    }

                    // Navigate to edit page using existing function
                    editar_game(selectedRandomGameId);
                }
            });
        }
    });
</script>
<?php #endregion JS random gaming ?>

<?php #endregion JS ?>
</body>
</html>
