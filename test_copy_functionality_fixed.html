<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Copy Functionality Test - 14 Character Limit</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-element {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            cursor: pointer;
            background-color: #2d2d2d;
        }
        .test-element:hover {
            background-color: #3d3d3d;
        }
        .tooltip-copied {
            position: absolute;
            background-color: #007bff;
            color: #fff;
            padding: 5px 10px;
            border-radius: 5px;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
            z-index: 1000;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #0d4f3c;
            border-radius: 3px;
            font-family: monospace;
        }
        .char-count {
            color: #ffc107;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Copy Functionality Test - 14 Character Limit</h1>
        <p>Click on the elements below to test the copy functionality. Team names will be limited to 14 characters.</p>
        
        <!-- Test 1: Home team with icon before -->
        <div class="test-element" id="home" onclick="copyAndShowTooltip(this.id)" title="Click to copy">
            <h4><i class="fas fa-home me-2"></i>Atlético Mineiro FC</h4>
            <div class="result" id="home-result">Copied text will appear here...</div>
        </div>
        
        <!-- Test 2: Away team with icon after -->
        <div class="test-element" id="away" onclick="copyAndShowTooltip(this.id)" title="Click to copy">
            <h4>Manchester United FC<i class="fas fa-plane ms-2"></i></h4>
            <div class="result" id="away-result">Copied text will appear here...</div>
        </div>
        
        <!-- Test 3: Modal home team -->
        <div class="test-element" id="mdl_home_team" onclick="copyAndShowTooltip(this.id)" title="Click to copy">
            <h5><i class="fas fa-home me-2"></i><span>Real Madrid CF</span></h5>
            <div class="result" id="mdl_home_team-result">Copied text will appear here...</div>
        </div>
        
        <!-- Test 4: Modal away team -->
        <div class="test-element" id="mdl_away_team" onclick="copyAndShowTooltip(this.id)" title="Click to copy">
            <h5><span>FC Barcelona</span><i class="fas fa-plane ms-2"></i></h5>
            <div class="result" id="mdl_away_team-result">Copied text will appear here...</div>
        </div>
        
        <!-- Test 5: Tournament (no icons) -->
        <div class="test-element" id="torneo" onclick="copyAndShowTooltip(this.id)" title="Click to copy">
            <span>Premier League 2023-24</span>
            <div class="result" id="torneo-result">Copied text will appear here...</div>
        </div>
        
        <!-- Test 6: Date/Time (no icons) -->
        <div class="test-element" id="fecha_hora" onclick="copyAndShowTooltip(this.id)" title="Click to copy">
            <span>2024-01-15 15:30</span>
            <div class="result" id="fecha_hora-result">Copied text will appear here...</div>
        </div>
    </div>

    <script>
        // Copy the enhanced copyAndShowTooltip function with 14-character limit
        function copyAndShowTooltip(controlId) {
            const element = document.getElementById(controlId);
            
            if (element) {
                let textToCopy = '';
                
                // Handle different elements with selective text copying
                if (controlId === 'home' || controlId === 'away' || controlId === 'mdl_home_team' || controlId === 'mdl_away_team') {
                    // For team names, extract only the actual team name without icons
                    // Clone the element to avoid modifying the original
                    const clonedElement = element.cloneNode(true);
                    
                    // Remove all icon elements (i tags with fa classes)
                    const icons = clonedElement.querySelectorAll('i[class*="fa"]');
                    icons.forEach(icon => icon.remove());
                    
                    // Get the cleaned text content and limit to first 14 characters
                    textToCopy = clonedElement.textContent.trim().substring(0, 14).trim();
                } else {
                    // For other elements, use cleaned textContent
                    textToCopy = element.textContent.trim();
                }
                
                // Copy the cleaned text to the clipboard
                navigator.clipboard.writeText(textToCopy).then(() => {
                    // Show the result in the test box
                    const resultElement = document.getElementById(controlId + '-result');
                    if (resultElement) {
                        resultElement.innerHTML = `Copied: "${textToCopy}" <span class="char-count">(${textToCopy.length} chars)</span>`;
                    }
                });
                
                // Create a tooltip element
                const tooltip = document.createElement("div");
                tooltip.textContent = "Copied!";
                tooltip.classList.add("tooltip-copied");
                document.body.appendChild(tooltip);
                
                // Position the tooltip
                const rect = element.getBoundingClientRect();
                tooltip.style.left = `${rect.left}px`;
                tooltip.style.top = `${rect.top - 20}px`;
                
                // Show the tooltip for 1 second
                tooltip.style.opacity = 1;
                setTimeout(() => {
                    tooltip.style.opacity = 0;
                    setTimeout(() => {
                        tooltip.remove();
                    }, 300); // Delay removal for smooth fading
                }, 1000);
            } else {
                console.error(`Element with ID "${controlId}" not found.`);
            }
        }
    </script>
</body>
</html>
